<?php $__env->startSection('title', $page->meta_title ?: $page->title . ' - QualifyRS'); ?>
<?php $__env->startSection('description', $page->meta_description ?: Str::limit(strip_tags($page->content), 160)); ?>
<?php $__env->startSection('keywords', $page->meta_keywords); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumb -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <div>
                            <a href="<?php echo e(route('web.home')); ?>" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                </svg>
                                <span class="sr-only">Home</span>
                            </a>
                        </div>
                    </li>
                    <?php if($page->category): ?>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="<?php echo e(route('web.pages.category', $page->category->slug)); ?>" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                                    <?php echo e($page->category->category); ?>

                                </a>
                            </div>
                        </li>
                    <?php endif; ?>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-4 text-sm font-medium text-gray-500"><?php echo e($page->title); ?></span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Article Content -->
            <article class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Article Header -->
                    <div class="px-6 py-8 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-4">
                            <?php if($page->category): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                                    <?php echo e($page->category->category); ?>

                                </span>
                            <?php endif; ?>
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <?php echo e(number_format($page->views)); ?> views
                            </div>
                        </div>
                        
                        <h1 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($page->title); ?></h1>
                        
                        <div class="flex items-center text-sm text-gray-500">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white"><?php echo e(substr($page->author->name, 0, 1)); ?></span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($page->author->name); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($page->created_at->format('M j, Y')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Article Body -->
                    <div class="px-6 py-8">
                        <div class="prose prose-lg max-w-none">
                            <?php echo $page->content; ?>

                        </div>
                    </div>

                    <!-- Tags -->
                    <?php if($page->tags->count() > 0): ?>
                        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div class="flex flex-wrap gap-2">
                                <span class="text-sm font-medium text-gray-700 mr-2">Tags:</span>
                                <?php $__currentLoopData = $page->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(route('web.pages.tag', $tag->slug)); ?>" 
                                       class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white hover:opacity-80 transition-opacity"
                                       style="background-color: <?php echo e($tag->color); ?>">
                                        <?php echo e($tag->name); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </article>

            <!-- Sidebar -->
            <aside class="lg:col-span-1">
                <div class="space-y-6">
                    <!-- Related Articles -->
                    <?php if(isset($relatedPages) && $relatedPages->count() > 0): ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $relatedPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedPage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex space-x-3">
                                        <div class="flex-1">
                                            <a href="<?php echo e($relatedPage->category ? route('web.pages.show', [$relatedPage->category->slug, $relatedPage->slug]) : route('web.pages.show.simple', $relatedPage->slug)); ?>" 
                                               class="block">
                                                <h4 class="text-sm font-medium text-gray-900 hover:text-indigo-600 line-clamp-2">
                                                    <?php echo e($relatedPage->title); ?>

                                                </h4>
                                                <p class="text-xs text-gray-500 mt-1">
                                                    <?php echo e($relatedPage->created_at->format('M j, Y')); ?>

                                                </p>
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Categories -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('web.pages')); ?>" class="block text-sm text-gray-600 hover:text-indigo-600">All Articles</a>
                            <?php
                                $categories = \App\Models\Category::active()->orderBy('category')->get();
                            ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e(route('web.pages.category', $category->slug)); ?>" 
                                   class="block text-sm text-gray-600 hover:text-indigo-600 <?php echo e($page->category && $page->category->id === $category->id ? 'font-medium text-indigo-600' : ''); ?>">
                                    <?php echo e($category->category); ?>

                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
                        <h3 class="text-lg font-semibold mb-2">Ready to Get Started?</h3>
                        <p class="text-sm mb-4 opacity-90">Join thousands of job seekers who have successfully landed their dream jobs with our help.</p>
                        <a href="<?php echo e(route('register')); ?>" class="inline-flex items-center px-4 py-2 bg-white text-indigo-600 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors">
                            Get Started Free
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </aside>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.web', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\qualifyrs\resources\views/web/pages/show.blade.php ENDPATH**/ ?>