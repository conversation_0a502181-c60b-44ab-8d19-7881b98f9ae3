<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Tag;

class CategoryTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Interview Categories
        $technical = Category::create([
            'category' => 'Technical Interview Questions',
            'slug' => 'technical-interview-questions',
            'description' => 'Technical interview questions for various programming roles',
            'status' => true,
            'sort_order' => 1
        ]);

        $webDev = Category::create([
            'category' => 'Web Development',
            'slug' => 'web-development-interview',
            'parent_category' => $technical->id,
            'description' => 'Web development interview questions',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Frontend Development',
            'slug' => 'frontend-development-interview',
            'parent_category' => $webDev->id,
            'description' => 'Frontend development interview questions',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Backend Development',
            'slug' => 'backend-development-interview',
            'parent_category' => $webDev->id,
            'description' => 'Backend development interview questions',
            'status' => true,
            'sort_order' => 2
        ]);

        Category::create([
            'category' => 'Full Stack Development',
            'slug' => 'full-stack-development-interview',
            'parent_category' => $webDev->id,
            'description' => 'Full stack development interview questions',
            'status' => true,
            'sort_order' => 3
        ]);



        $mobile = Category::create([
            'category' => 'Mobile Development',
            'slug' => 'mobile-development-interview',
            'parent_category' => $technical->id,
            'description' => 'Mobile development interview questions',
            'status' => true,
            'sort_order' => 2
        ]);

        Category::create([
            'category' => 'iOS Development',
            'slug' => 'ios-development-interview',
            'parent_category' => $mobile->id,
            'description' => 'iOS development interview questions',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Android Development',
            'slug' => 'android-development-interview',
            'parent_category' => $mobile->id,
            'description' => 'Android development interview questions',
            'status' => true,
            'sort_order' => 2
        ]);

        $dataScience = Category::create([
            'category' => 'Data Science & Analytics',
            'slug' => 'data-science-interview',
            'parent_category' => $technical->id,
            'description' => 'Data science and analytics interview questions',
            'status' => true,
            'sort_order' => 3
        ]);

        $hr = Category::create([
            'category' => 'HR & Behavioral Questions',
            'slug' => 'hr-behavioral-interview',
            'description' => 'HR and behavioral interview questions',
            'status' => true,
            'sort_order' => 2
        ]);

        Category::create([
            'category' => 'Leadership Questions',
            'slug' => 'leadership-interview',
            'parent_category' => $hr->id,
            'description' => 'Leadership and management interview questions',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Teamwork Questions',
            'slug' => 'teamwork-interview',
            'parent_category' => $hr->id,
            'description' => 'Teamwork and collaboration interview questions',
            'status' => true,
            'sort_order' => 2
        ]);

        $general = Category::create([
            'category' => 'General Interview Questions',
            'slug' => 'general-interview',
            'description' => 'General interview questions for all roles',
            'status' => true,
            'sort_order' => 3
        ]);

        // Create Interview Tags
        $tags = [
            // Programming Languages
            ['name' => 'PHP', 'color' => '#777BB4'],
            ['name' => 'JavaScript', 'color' => '#F7DF1E'],
            ['name' => 'Python', 'color' => '#3776AB'],
            ['name' => 'Java', 'color' => '#ED8B00'],
            ['name' => 'C#', 'color' => '#239120'],
            ['name' => 'Swift', 'color' => '#FA7343'],
            ['name' => 'Kotlin', 'color' => '#0095D5'],

            // Frameworks & Libraries
            ['name' => 'Laravel', 'color' => '#FF2D20'],
            ['name' => 'React', 'color' => '#61DAFB'],
            ['name' => 'Vue.js', 'color' => '#4FC08D'],
            ['name' => 'Angular', 'color' => '#DD0031'],
            ['name' => 'Node.js', 'color' => '#339933'],
            ['name' => 'Django', 'color' => '#092E20'],
            ['name' => 'Spring Boot', 'color' => '#6DB33F'],

            // Databases
            ['name' => 'MySQL', 'color' => '#4479A1'],
            ['name' => 'PostgreSQL', 'color' => '#336791'],
            ['name' => 'MongoDB', 'color' => '#47A248'],
            ['name' => 'Redis', 'color' => '#DC382D'],

            // Difficulty Levels
            ['name' => 'Beginner', 'color' => '#4CAF50'],
            ['name' => 'Intermediate', 'color' => '#FF9800'],
            ['name' => 'Advanced', 'color' => '#FF5722'],
            ['name' => 'Expert', 'color' => '#9C27B0'],

            // Question Types
            ['name' => 'Technical', 'color' => '#2196F3'],
            ['name' => 'Behavioral', 'color' => '#9C27B0'],
            ['name' => 'Problem Solving', 'color' => '#FF5722'],
            ['name' => 'System Design', 'color' => '#795548'],
            ['name' => 'Coding Challenge', 'color' => '#607D8B'],

            // Experience Levels
            ['name' => 'Entry Level', 'color' => '#4CAF50'],
            ['name' => 'Mid Level', 'color' => '#FF9800'],
            ['name' => 'Senior Level', 'color' => '#FF5722'],
            ['name' => 'Lead Level', 'color' => '#9C27B0'],
        ];

        foreach ($tags as $tag) {
            Tag::create([
                'name' => $tag['name'],
                'slug' => \Str::slug($tag['name']),
                'color' => $tag['color'],
                'status' => true,
            ]);
        }
    }
}
