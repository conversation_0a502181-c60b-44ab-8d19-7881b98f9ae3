<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    public function __construct()
    {
    }

    public function index()
    {
        $categories = Category::with(['parent', 'children'])
                             ->orderBy('sort_order')
                             ->paginate(15);

        return view('categories.index', compact('categories'));
    }

    public function create()
    {
        $parentCategories = Category::whereNull('parent_category')
                                   ->orWhere('parent_category', 0)
                                   ->orderBy('category')
                                   ->get();

        return view('categories.create', compact('parentCategories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'category' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories',
            'parent_category' => 'nullable|exists:categories,id',
            'description' => 'nullable|string',
            'status' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $slug = $request->slug ?: Str::slug($request->category);

        // Ensure unique slug
        $originalSlug = $slug;
        $counter = 1;
        while (Category::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        Category::create([
            'category' => $request->category,
            'slug' => $slug,
            'parent_category' => $request->parent_category,
            'description' => $request->description,
            'status' => $request->has('status'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('categories.index')
            ->with('success', 'Category created successfully.');
    }

    public function show(Category $category)
    {
        $category->load(['parent', 'children', 'pages']);
        return view('categories.show', compact('category'));
    }

    public function edit(Category $category)
    {
        $parentCategories = Category::where('id', '!=', $category->id)
                                   ->whereNull('parent_category')
                                   ->orWhere('parent_category', 0)
                                   ->orderBy('category')
                                   ->get();

        return view('categories.edit', compact('category', 'parentCategories'));
    }

    public function update(Request $request, Category $category)
    {
        $request->validate([
            'category' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories,slug,' . $category->id,
            'parent_category' => 'nullable|exists:categories,id',
            'description' => 'nullable|string',
            'status' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        // Prevent circular reference
        if ($request->parent_category == $category->id) {
            return back()->withErrors(['parent_category' => 'A category cannot be its own parent.']);
        }

        $data = $request->all();
        $data['status'] = $request->has('status');

        if ($request->category !== $category->category && !$request->slug) {
            $slug = Str::slug($request->category);
            $originalSlug = $slug;
            $counter = 1;
            while (Category::where('slug', $slug)->where('id', '!=', $category->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $data['slug'] = $slug;
        }

        $category->update($data);

        return redirect()->route('categories.index')
            ->with('success', 'Category updated successfully.');
    }

    public function destroy(Category $category)
    {
        // Check if category has children
        if ($category->children()->count() > 0) {
            return redirect()->route('categories.index')
                ->with('error', 'Cannot delete category with subcategories. Please delete or move subcategories first.');
        }

        // Check if category has pages
        if ($category->pages()->count() > 0) {
            return redirect()->route('categories.index')
                ->with('error', 'Cannot delete category with pages. Please move or delete pages first.');
        }

        $category->delete();

        return redirect()->route('categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    // API method to get categories for AJAX requests
    public function getSubcategories(Category $category)
    {
        return response()->json($category->children);
    }
}
