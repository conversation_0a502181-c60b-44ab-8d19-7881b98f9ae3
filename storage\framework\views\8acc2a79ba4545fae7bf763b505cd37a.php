<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Create New Page')); ?>

            </h2>
            <a href="<?php echo e(route('pages.manage')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Back to Manage
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="px-4 sm:px-6 lg:px-8">
            <form action="<?php echo e(route('pages.store')); ?>" method="POST" x-data="pageForm()">
                <?php echo csrf_field(); ?>

                <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                    <!-- Main Content -->
                    <div class="xl:col-span-3 space-y-6">
                        <!-- Basic Information Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Basic Information</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Title -->
                                    <div class="md:col-span-2">
                                        <label for="title" class="form-label">Page Title *</label>
                                        <input type="text"
                                               id="title"
                                               name="title"
                                               value="<?php echo e(old('title')); ?>"
                                               class="form-input"
                                               required
                                               autofocus
                                               x-model="title"
                                               @input="generateSlug"
                                               placeholder="Enter an engaging title for your page">
                                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <!-- Slug -->
                                    <div class="md:col-span-2">
                                        <label for="slug" class="form-label">URL Slug</label>
                                        <div class="relative">
                                            <input type="text"
                                                   id="slug"
                                                   name="slug"
                                                   value="<?php echo e(old('slug')); ?>"
                                                   class="form-input pl-20"
                                                   x-model="slug"
                                                   placeholder="auto-generated-from-title">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 text-sm">/pages/</span>
                                            </div>
                                        </div>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">URL-friendly version of the title. Leave blank to auto-generate.</p>
                                        <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Page Content</h3>
                                <div class="form-group">
                                    <?php if (isset($component)) { $__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.quill-editor','data' => ['name' => 'content','value' => old('content'),'placeholder' => 'Start writing your page content here...','height' => '450px','required' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('quill-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'content','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('content')),'placeholder' => 'Start writing your page content here...','height' => '450px','required' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c)): ?>
<?php $attributes = $__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c; ?>
<?php unset($__attributesOriginal030291ebfbe01b7b21fc10c5b0c01e4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c)): ?>
<?php $component = $__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c; ?>
<?php unset($__componentOriginal030291ebfbe01b7b21fc10c5b0c01e4c); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- SEO & Meta Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">SEO & Meta Information</h3>
                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea id="meta_description"
                                              name="meta_description"
                                              rows="3"
                                              class="form-textarea"
                                              maxlength="160"
                                              placeholder="Write a compelling description for search engines..."><?php echo e(old('meta_description')); ?></textarea>
                                    <div class="flex justify-between mt-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Brief description for search engines and social media.</p>
                                        <span class="text-xs text-gray-400" x-text="$el.previousElementSibling.value.length + '/160'"></span>
                                    </div>
                                    <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="form-group">
                                    <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                    <input type="text"
                                           id="meta_keywords"
                                           name="meta_keywords"
                                           value="<?php echo e(old('meta_keywords')); ?>"
                                           class="form-input"
                                           placeholder="keyword1, keyword2, keyword3">
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Comma-separated keywords related to this page.</p>
                                    <?php $__errorArgs = ['meta_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Publish Settings -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Publish Settings</h3>
                                <div class="space-y-4">
                                    <!-- Status -->
                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                        <div>
                                            <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Publish Status</label>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">Make this page visible to visitors</p>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" name="status" value="1"
                                                   class="sr-only peer"
                                                   <?php echo e(old('status') ? 'checked' : ''); ?>>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                                        </label>
                                    </div>

                                    <!-- Featured -->
                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                        <div>
                                            <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Featured Page</label>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">Highlight this page on homepage</p>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" name="featured" value="1"
                                                   class="sr-only peer"
                                                   <?php echo e(old('featured') ? 'checked' : ''); ?>>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-500"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Category</h3>
                                <div class="form-group">
                                    <select name="category_id" class="form-select">
                                        <option value="">Choose a category...</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->category); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Tags -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Tags</h3>
                                <div x-data="tagSelector()" class="space-y-3">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                            </svg>
                                        </div>
                                        <input type="text"
                                               x-model="search"
                                               @input="searchTags"
                                               @keydown.enter.prevent="addTag"
                                               placeholder="Search and select tags..."
                                               class="form-input pl-10">

                                        <div x-show="showDropdown && filteredTags.length > 0"
                                             class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-40 overflow-y-auto">
                                            <template x-for="tag in filteredTags" :key="tag.id">
                                                <div @click="selectTag(tag)"
                                                     class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between">
                                                    <span x-text="tag.name" class="text-gray-900 dark:text-gray-100"></span>
                                                    <span class="w-3 h-3 rounded-full" :style="`background-color: ${tag.color}`"></span>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap gap-2 min-h-[2rem]">
                                        <template x-for="tag in selectedTags" :key="tag.id">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white shadow-sm"
                                                  :style="`background-color: ${tag.color};`">
                                                <span x-text="tag.name"></span>
                                                <button type="button" @click="removeTag(tag.id)" class="ml-2 text-white hover:text-gray-200">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                    </svg>
                                                </button>
                                                <input type="hidden" name="tags[]" :value="tag.id">
                                            </span>
                                        </template>
                                        <div x-show="selectedTags.length === 0" class="text-sm text-gray-500 dark:text-gray-400 py-1">
                                            No tags selected
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="form-card">
                            <div class="form-section">
                                <div class="space-y-3">
                                    <button type="submit" class="btn-primary w-full">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Create Page
                                    </button>
                                    <a href="<?php echo e(route('pages.manage')); ?>" class="btn-outline w-full text-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        function pageForm() {
            return {
                title: '',
                slug: '',
                generateSlug() {
                    if (this.title && !this.slug) {
                        this.slug = this.title.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                }
            }
        }

        function tagSelector() {
            return {
                search: '',
                showDropdown: false,
                selectedTags: [],
                filteredTags: [],
                availableTags: <?php echo json_encode($tags, 15, 512) ?>,
                
                searchTags() {
                    if (this.search.length > 0) {
                        this.filteredTags = this.availableTags.filter(tag => 
                            tag.name.toLowerCase().includes(this.search.toLowerCase()) &&
                            !this.selectedTags.find(selected => selected.id === tag.id)
                        );
                        this.showDropdown = true;
                    } else {
                        this.showDropdown = false;
                    }
                },
                
                selectTag(tag) {
                    this.selectedTags.push(tag);
                    this.search = '';
                    this.showDropdown = false;
                },
                
                removeTag(tagId) {
                    this.selectedTags = this.selectedTags.filter(tag => tag.id !== tagId);
                },
                
                addTag() {
                    if (this.filteredTags.length > 0) {
                        this.selectTag(this.filteredTags[0]);
                    }
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\qualifyrs\resources\views/backend/pages/create.blade.php ENDPATH**/ ?>