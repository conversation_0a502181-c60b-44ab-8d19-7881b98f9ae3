<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\Category;
use App\Models\Tag;
use App\Models\User;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            return;
        }

        // Get categories
        $webDevCategory = Category::where('slug', 'web-development-interview')->first();
        $backendCategory = Category::where('slug', 'backend-development-interview')->first();
        $frontendCategory = Category::where('slug', 'frontend-development-interview')->first();

        if (!$webDevCategory || !$backendCategory || !$frontendCategory) {
            return;
        }

        // Get some tags
        $jsTag = Tag::where('slug', 'javascript')->first();
        $reactTag = Tag::where('slug', 'react')->first();
        $nodeTag = Tag::where('slug', 'nodejs')->first();
        $phpTag = Tag::where('slug', 'php')->first();

        // Create sample pages
        $pages = [
            [
                'title' => 'Complete Guide to JavaScript Interview Questions',
                'slug' => 'complete-guide-javascript-interview-questions',
                'content' => $this->getJavaScriptContent(),
                'excerpt' => 'Master JavaScript interviews with our comprehensive guide covering ES6+, async programming, closures, and more.',
                'meta_title' => 'JavaScript Interview Questions & Answers 2024 - Complete Guide',
                'meta_description' => 'Ace your JavaScript interview with 100+ questions covering ES6, async/await, closures, prototypes, and modern JS concepts.',
                'category_id' => $frontendCategory->id,
                'author_id' => $admin->id,
                'status' => true,
                'published_at' => now(),
                'views' => rand(100, 1000),
                'tags' => [$jsTag?->id, $reactTag?->id]
            ],
            [
                'title' => 'React Interview Questions for Senior Developers',
                'slug' => 'react-interview-questions-senior-developers',
                'content' => $this->getReactContent(),
                'excerpt' => 'Advanced React interview questions covering hooks, performance optimization, state management, and architectural patterns.',
                'meta_title' => 'React Interview Questions for Senior Developers - 2024',
                'meta_description' => 'Advanced React interview questions and answers for senior developers. Covers hooks, performance, state management, and more.',
                'category_id' => $frontendCategory->id,
                'author_id' => $admin->id,
                'status' => true,
                'published_at' => now(),
                'views' => rand(100, 1000),
                'tags' => [$reactTag?->id, $jsTag?->id]
            ],
            [
                'title' => 'Node.js Backend Interview Questions and Answers',
                'slug' => 'nodejs-backend-interview-questions-answers',
                'content' => $this->getNodeJSContent(),
                'excerpt' => 'Comprehensive Node.js interview preparation covering Express, databases, authentication, and scalability.',
                'meta_title' => 'Node.js Interview Questions & Answers - Backend Developer Guide',
                'meta_description' => 'Master Node.js interviews with questions on Express, MongoDB, authentication, microservices, and performance optimization.',
                'category_id' => $backendCategory->id,
                'author_id' => $admin->id,
                'status' => true,
                'published_at' => now(),
                'views' => rand(100, 1000),
                'tags' => [$nodeTag?->id, $jsTag?->id]
            ],
            [
                'title' => 'PHP Laravel Interview Questions for Web Developers',
                'slug' => 'php-laravel-interview-questions-web-developers',
                'content' => $this->getLaravelContent(),
                'excerpt' => 'Essential PHP and Laravel interview questions covering MVC, Eloquent, middleware, and modern PHP practices.',
                'meta_title' => 'PHP Laravel Interview Questions - Web Developer Guide 2024',
                'meta_description' => 'Prepare for PHP Laravel interviews with questions on Eloquent, middleware, routing, testing, and best practices.',
                'category_id' => $backendCategory->id,
                'author_id' => $admin->id,
                'status' => true,
                'published_at' => now(),
                'views' => rand(100, 1000),
                'tags' => [$phpTag?->id]
            ]
        ];

        foreach ($pages as $pageData) {
            $tags = $pageData['tags'] ?? [];
            unset($pageData['tags']);

            $page = Page::create($pageData);

            // Attach tags if they exist
            if (!empty($tags)) {
                $validTags = array_filter($tags);
                if (!empty($validTags)) {
                    $page->tags()->attach($validTags);
                }
            }
        }
    }

    private function getJavaScriptContent()
    {
        return '<h2>JavaScript Interview Questions</h2>
        
        <p>JavaScript remains one of the most popular programming languages, and mastering it is crucial for any web developer. This comprehensive guide covers the most important JavaScript interview questions you\'re likely to encounter.</p>
        
        <h3>1. What are the different data types in JavaScript?</h3>
        <p>JavaScript has several primitive data types:</p>
        <ul>
            <li><strong>Number:</strong> Represents both integers and floating-point numbers</li>
            <li><strong>String:</strong> Represents text data</li>
            <li><strong>Boolean:</strong> Represents true or false</li>
            <li><strong>Undefined:</strong> Variable declared but not assigned</li>
            <li><strong>Null:</strong> Intentional absence of value</li>
            <li><strong>Symbol:</strong> Unique identifier (ES6+)</li>
            <li><strong>BigInt:</strong> Large integers (ES2020)</li>
        </ul>
        
        <h3>2. Explain closures in JavaScript</h3>
        <p>A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned. Closures are created every time a function is created.</p>
        
        <h3>3. What is the difference between let, const, and var?</h3>
        <p>These are different ways to declare variables in JavaScript:</p>
        <ul>
            <li><strong>var:</strong> Function-scoped, can be redeclared and updated</li>
            <li><strong>let:</strong> Block-scoped, can be updated but not redeclared</li>
            <li><strong>const:</strong> Block-scoped, cannot be updated or redeclared</li>
        </ul>
        
        <h3>4. Explain async/await and Promises</h3>
        <p>Promises represent the eventual completion or failure of an asynchronous operation. Async/await is syntactic sugar that makes working with promises more readable and easier to understand.</p>
        
        <p>Continue reading for more advanced topics including prototypes, event delegation, and performance optimization techniques.</p>';
    }

    private function getReactContent()
    {
        return '<h2>React Interview Questions for Senior Developers</h2>
        
        <p>React has evolved significantly over the years, and senior developers are expected to understand not just the basics, but also advanced patterns, performance optimization, and architectural decisions.</p>
        
        <h3>1. Explain React Hooks and their benefits</h3>
        <p>Hooks allow you to use state and other React features in functional components. Key benefits include:</p>
        <ul>
            <li>Reusable stateful logic between components</li>
            <li>Simpler component structure</li>
            <li>Better performance optimization</li>
            <li>Easier testing</li>
        </ul>
        
        <h3>2. What is the Virtual DOM and how does it work?</h3>
        <p>The Virtual DOM is a JavaScript representation of the actual DOM. React uses it to optimize rendering by comparing the new virtual DOM tree with the previous one and only updating the parts that have changed.</p>
        
        <h3>3. Explain React Context and when to use it</h3>
        <p>React Context provides a way to pass data through the component tree without having to pass props down manually at every level. Use it for truly global data like themes, authentication, or language preferences.</p>
        
        <h3>4. How do you optimize React performance?</h3>
        <p>Several techniques for optimizing React performance:</p>
        <ul>
            <li>Use React.memo for component memoization</li>
            <li>Implement useMemo and useCallback hooks</li>
            <li>Code splitting with React.lazy</li>
            <li>Optimize bundle size</li>
            <li>Use proper key props in lists</li>
        </ul>
        
        <p>This guide covers advanced topics including custom hooks, error boundaries, and state management patterns.</p>';
    }

    private function getNodeJSContent()
    {
        return '<h2>Node.js Backend Interview Questions</h2>
        
        <p>Node.js has become a popular choice for backend development. This guide covers essential concepts and common interview questions for Node.js developers.</p>
        
        <h3>1. What is Node.js and how does it work?</h3>
        <p>Node.js is a JavaScript runtime built on Chrome\'s V8 JavaScript engine. It uses an event-driven, non-blocking I/O model that makes it lightweight and efficient for building scalable network applications.</p>
        
        <h3>2. Explain the Event Loop in Node.js</h3>
        <p>The Event Loop is what allows Node.js to perform non-blocking I/O operations. It continuously checks the call stack and processes callbacks from the event queue when the stack is empty.</p>
        
        <h3>3. What are Streams in Node.js?</h3>
        <p>Streams are objects that let you read data from a source or write data to a destination in a continuous fashion. There are four types:</p>
        <ul>
            <li>Readable streams</li>
            <li>Writable streams</li>
            <li>Duplex streams</li>
            <li>Transform streams</li>
        </ul>
        
        <h3>4. How do you handle errors in Node.js?</h3>
        <p>Error handling in Node.js can be done through:</p>
        <ul>
            <li>Try-catch blocks for synchronous code</li>
            <li>Error-first callbacks</li>
            <li>Promise rejection handling</li>
            <li>Global error handlers</li>
        </ul>
        
        <p>Learn more about Express.js, database integration, authentication, and deployment strategies.</p>';
    }

    private function getLaravelContent()
    {
        return '<h2>PHP Laravel Interview Questions</h2>
        
        <p>Laravel is one of the most popular PHP frameworks. This guide covers essential Laravel concepts and common interview questions for web developers.</p>
        
        <h3>1. What is Laravel and its key features?</h3>
        <p>Laravel is a PHP web application framework with expressive, elegant syntax. Key features include:</p>
        <ul>
            <li>Eloquent ORM</li>
            <li>Blade templating engine</li>
            <li>Artisan command-line interface</li>
            <li>Built-in authentication</li>
            <li>Middleware support</li>
            <li>Queue management</li>
        </ul>
        
        <h3>2. Explain Laravel\'s MVC architecture</h3>
        <p>Laravel follows the Model-View-Controller (MVC) pattern:</p>
        <ul>
            <li><strong>Model:</strong> Represents data and business logic</li>
            <li><strong>View:</strong> Handles the presentation layer</li>
            <li><strong>Controller:</strong> Manages user input and coordinates between Model and View</li>
        </ul>
        
        <h3>3. What is Eloquent ORM?</h3>
        <p>Eloquent is Laravel\'s built-in ORM that provides a beautiful, simple ActiveRecord implementation for working with your database. Each database table has a corresponding "Model" that is used to interact with that table.</p>
        
        <h3>4. Explain Laravel Middleware</h3>
        <p>Middleware provides a convenient mechanism for filtering HTTP requests entering your application. Common uses include authentication, CORS handling, and request logging.</p>
        
        <p>Explore advanced topics including service containers, facades, events, and testing strategies.</p>';
    }
}
