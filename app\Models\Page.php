<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Page extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'meta_description',
        'meta_keywords',
        'meta_title',
        'excerpt',
        'status',
        'featured_image',
        'featured_image_alt',
        'author_id',
        'category_id',
        'views',
        'published_at'
    ];

    protected $casts = [
        'status' => 'boolean',
        'published_at' => 'datetime',
    ];

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    // Removed - duplicate method below

    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Belongs to one category
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Many-to-many relationship with tags
    public function tags()
    {
        return $this->belongsToMany(Tag::class);
    }

    // Sync tags and update usage counts
    public function syncTags($tagIds)
    {
        // Get old tags to update their counts
        $oldTags = $this->tags;

        // Sync the new tags
        $this->tags()->sync($tagIds);

        // Update usage counts for old tags
        foreach ($oldTags as $tag) {
            $tag->updateUsageCount();
        }

        // Update usage counts for new tags
        $newTags = Tag::whereIn('id', $tagIds)->get();
        foreach ($newTags as $tag) {
            $tag->updateUsageCount();
        }
    }

    // Get tag names as comma-separated string
    public function getTagNamesAttribute()
    {
        return $this->tags->pluck('name')->implode(', ');
    }

    // Get category breadcrumb
    public function getCategoryBreadcrumbAttribute()
    {
        return $this->category ? $this->category->full_path : 'Uncategorized';
    }

    // SEO-friendly URL
    public function getUrlAttribute()
    {
        if ($this->category) {
            return route('pages.show', [$this->category->slug, $this->slug]);
        }
        return route('pages.show.simple', $this->slug);
    }

    // Get SEO title (meta_title or title)
    public function getSeoTitleAttribute()
    {
        return $this->meta_title ?: $this->title;
    }

    // Get SEO description (meta_description or excerpt)
    public function getSeoDescriptionAttribute()
    {
        return $this->meta_description ?: $this->excerpt ?: \Str::limit(strip_tags($this->content), 160);
    }

    // Get reading time estimate
    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $minutes = ceil($wordCount / 200); // Average reading speed
        return $minutes . ' min read';
    }

    // Scope for published pages with published_at
    public function scopePublished($query)
    {
        return $query->where('status', true)
                    ->where(function($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '<=', now());
                    });
    }
}
