<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Role') }}
            </h2>
            <a href="{{ route('roles.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Back to Roles
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('roles.store') }}" method="POST" class="space-y-6">
                        @csrf

                        <!-- Role Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Role Name *
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('name') border-red-500 @enderror"
                                   placeholder="Enter role name (e.g., moderator, reviewer)">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Role name should be lowercase and unique. It will be used internally by the system.
                            </p>
                        </div>

                        <!-- Permissions -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                Permissions
                            </label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    @php
                                        $groupedPermissions = $permissions->groupBy(function($permission) {
                                            return explode('-', $permission->name)[0];
                                        });
                                    @endphp

                                    @foreach($groupedPermissions as $group => $groupPermissions)
                                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                                            <h4 class="font-medium text-gray-900 mb-3 capitalize">
                                                {{ str_replace('_', ' ', $group) }}
                                            </h4>
                                            <div class="space-y-2">
                                                @foreach($groupPermissions as $permission)
                                                    <label class="flex items-center">
                                                        <input type="checkbox" 
                                                               name="permissions[]" 
                                                               value="{{ $permission->name }}"
                                                               {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}
                                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                                        <span class="ml-2 text-sm text-gray-700">
                                                            {{ ucwords(str_replace(['-', '_'], ' ', $permission->name)) }}
                                                        </span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                @error('permissions')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror

                                <div class="mt-4 p-3 bg-blue-50 rounded-md">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm text-blue-700">
                                                <strong>Tip:</strong> Select only the permissions that users with this role should have. 
                                                You can always modify permissions later.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Permission Sets -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">
                                Quick Permission Sets
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <button type="button" 
                                        onclick="selectPermissionSet('content')"
                                        class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <div class="text-center">
                                        <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        <h4 class="text-sm font-medium text-gray-900">Content Manager</h4>
                                        <p class="text-xs text-gray-500 mt-1">Manage pages, questions, and content</p>
                                    </div>
                                </button>

                                <button type="button" 
                                        onclick="selectPermissionSet('quiz')"
                                        class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <div class="text-center">
                                        <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                        </svg>
                                        <h4 class="text-sm font-medium text-gray-900">Quiz Manager</h4>
                                        <p class="text-xs text-gray-500 mt-1">Create and manage quizzes</p>
                                    </div>
                                </button>

                                <button type="button" 
                                        onclick="selectPermissionSet('viewer')"
                                        class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <div class="text-center">
                                        <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <h4 class="text-sm font-medium text-gray-900">Viewer</h4>
                                        <p class="text-xs text-gray-500 mt-1">View-only access</p>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="{{ route('roles.index') }}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Create Role
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectPermissionSet(type) {
            // Uncheck all checkboxes first
            document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Define permission sets
            const permissionSets = {
                content: [
                    'view-dashboard',
                    'manage-pages', 'create-pages', 'edit-pages', 'delete-pages', 'publish-pages',
                    'manage-questions', 'create-questions', 'edit-questions', 'delete-questions', 'publish-questions',
                    'manage-categories', 'create-categories', 'edit-categories', 'delete-categories',
                    'manage-tags', 'create-tags', 'edit-tags', 'delete-tags'
                ],
                quiz: [
                    'view-dashboard',
                    'manage-quizzes', 'create-quizzes', 'edit-quizzes', 'delete-quizzes', 'publish-quizzes',
                    'view-quiz-results', 'take-quizzes'
                ],
                viewer: [
                    'view-dashboard', 'view-questions', 'take-quizzes'
                ]
            };

            // Check relevant permissions
            if (permissionSets[type]) {
                permissionSets[type].forEach(permission => {
                    const checkbox = document.querySelector(`input[value="${permission}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
        }
    </script>
</x-app-layout>
