<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\InterviewController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuizController;
use Illuminate\Support\Facades\Route;

Route::get('/', [App\Http\Controllers\WebController::class, 'home'])->name('web.home');

Route::get('/dashboard', function () {
    $user = auth()->user();

    // Admin dashboard with full stats
    if ($user->hasRole('admin')) {
        $stats = [
            'users' => \App\Models\User::count(),
            'pages' => \App\Models\Page::count(),
            'categories' => \App\Models\Category::count(),
            'tags' => \App\Models\Tag::count(),
            'questions' => \App\Models\Question::count(),
            'quizzes' => \App\Models\Quiz::count(),
            'roles' => \Spatie\Permission\Models\Role::count(),
            'permissions' => \Spatie\Permission\Models\Permission::count(),
        ];

        $recentUsers = \App\Models\User::latest()->take(5)->get();
        $recentPages = \App\Models\Page::with(['category', 'author'])->latest()->take(5)->get();
        $recentQuestions = \App\Models\Question::with(['category', 'author'])->latest()->take(5)->get();
        $recentCategories = \App\Models\Category::latest()->take(5)->get();
        $popularTags = \App\Models\Tag::popular(5)->get();

        return view('dashboard', compact('stats', 'recentUsers', 'recentPages', 'recentQuestions', 'recentCategories', 'popularTags'));
    }

    // Manager dashboard with limited stats
    if ($user->hasRole('manager')) {
        $stats = [
            'my_pages' => \App\Models\Page::where('author_id', $user->id)->count(),
            'published_pages' => \App\Models\Page::where('author_id', $user->id)->where('status', true)->count(),
            'draft_pages' => \App\Models\Page::where('author_id', $user->id)->where('status', false)->count(),
            'my_questions' => \App\Models\Question::where('author_id', $user->id)->count(),
            'my_quizzes' => \App\Models\Quiz::where('author_id', $user->id)->count(),
        ];

        $myRecentPages = \App\Models\Page::with(['category'])->where('author_id', $user->id)->latest()->take(5)->get();
        $myRecentQuestions = \App\Models\Question::with(['category'])->where('author_id', $user->id)->latest()->take(5)->get();

        return view('dashboard', compact('stats', 'myRecentPages', 'myRecentQuestions'));
    }

    // Regular user dashboard
    return view('dashboard');
})->middleware(['auth'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Management routes (role-based access)
Route::middleware(['auth'])->group(function () {
    // User management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('users', UserManagementController::class);
        Route::resource('roles', RoleController::class);
    });

    // Category management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('categories', CategoryController::class);
        Route::get('categories/{category}/subcategories', [CategoryController::class, 'getSubcategories'])->name('categories.subcategories');
    });

    // Tag management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('tags', TagController::class);
        Route::get('tags/search', [TagController::class, 'search'])->name('tags.search');
    });

    // Page management (admin and manager)
    Route::middleware('role:admin|manager')->group(function () {
        Route::get('manage/pages', [PageController::class, 'adminIndex'])->name('pages.manage');
        Route::get('manage/pages/create', [PageController::class, 'create'])->name('pages.create');
        Route::post('manage/pages', [PageController::class, 'store'])->name('pages.store');
        Route::get('manage/pages/{page}/edit', [PageController::class, 'edit'])->name('pages.edit');
        Route::put('manage/pages/{page}', [PageController::class, 'update'])->name('pages.update');
        Route::delete('manage/pages/{page}', [PageController::class, 'destroy'])->name('pages.destroy');
    });

    // Question management (admin and manager)
    Route::middleware('role:admin|manager')->group(function () {
        Route::resource('questions', QuestionController::class);
        Route::get('questions/{question}/options', [QuestionController::class, 'getOptions'])->name('questions.options');
    });

    // Quiz management (admin and manager)
    Route::middleware('role:admin|manager')->group(function () {
        Route::resource('quizzes', QuizController::class);
        Route::get('quizzes/{quiz}/questions', [QuizController::class, 'manageQuestions'])->name('quizzes.questions');
        Route::post('quizzes/{quiz}/questions', [QuizController::class, 'addQuestion'])->name('quizzes.questions.add');
        Route::delete('quizzes/{quiz}/questions/{question}', [QuizController::class, 'removeQuestion'])->name('quizzes.questions.remove');
    });
});

require __DIR__.'/auth.php';

// Public Interview Questions Routes
Route::get('/interview-questions', [InterviewController::class, 'index'])->name('interview.index');
Route::get('/interview-questions/search', [InterviewController::class, 'search'])->name('interview.search');
Route::get('/interview-questions/difficulty/{difficulty}', [InterviewController::class, 'byDifficulty'])->name('interview.difficulty');
Route::get('/interview-questions/category/{category}', [InterviewController::class, 'category'])->name('interview.category');
Route::get('/interview-questions/tag/{tag}', [InterviewController::class, 'byTag'])->name('interview.tag');
Route::get('/interview-questions/{question}', [InterviewController::class, 'show'])->name('interview.show');

// Public website routes
Route::get('/pages', [App\Http\Controllers\WebController::class, 'pages'])->name('web.pages');
Route::get('/category/{category}', [App\Http\Controllers\WebController::class, 'pagesByCategory'])->name('web.pages.category');
Route::get('/tag/{tag}', [App\Http\Controllers\WebController::class, 'pagesByTag'])->name('web.pages.tag');
Route::get('/about', [App\Http\Controllers\WebController::class, 'about'])->name('web.about');
Route::get('/contact', [App\Http\Controllers\WebController::class, 'contact'])->name('web.contact');
Route::get('/services', [App\Http\Controllers\WebController::class, 'services'])->name('web.services');

// SEO-friendly public page routes (must be last to avoid conflicts)
Route::get('/{category}/{page}', [App\Http\Controllers\WebController::class, 'showPage'])->name('web.pages.show')->where(['category' => '[a-zA-Z0-9\-]+', 'page' => '[a-zA-Z0-9\-]+']);
Route::get('/{page}', [App\Http\Controllers\WebController::class, 'showPageWithoutCategory'])->name('web.pages.show.simple')->where('page', '[a-zA-Z0-9\-]+');
