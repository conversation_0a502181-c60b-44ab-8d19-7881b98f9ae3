<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Question;
use App\Models\QuestionOption;
use App\Models\Category;
use App\Models\User;

class QuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            return;
        }

        // Get categories
        $frontendCategory = Category::where('slug', 'frontend-development-interview')->first();
        $backendCategory = Category::where('slug', 'backend-development-interview')->first();
        $hrCategory = Category::where('slug', 'hr-behavioral-interview')->first();

        if (!$frontendCategory || !$backendCategory || !$hrCategory) {
            return;
        }

        // Frontend Questions
        $this->createFrontendQuestions($admin, $frontendCategory);

        // Backend Questions
        $this->createBackendQuestions($admin, $backendCategory);

        // HR Questions
        $this->createHRQuestions($admin, $hrCategory);
    }

    private function createFrontendQuestions($admin, $category)
    {
        // JavaScript Question
        $question1 = Question::create([
            'title' => 'What is the difference between let, const, and var in JavaScript?',
            'question' => 'Explain the differences between let, const, and var keywords in JavaScript, including their scope, hoisting behavior, and when to use each.',
            'answer' => 'var has function scope and is hoisted, let has block scope and is hoisted but not initialized, const has block scope, is hoisted but not initialized, and cannot be reassigned.',
            'explanation' => 'var is function-scoped and hoisted with undefined initialization. let and const are block-scoped and hoisted but not initialized (temporal dead zone). const cannot be reassigned after declaration.',
            'type' => 'multiple_choice',
            'difficulty' => 'intermediate',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'var is function-scoped, let and const are block-scoped, const cannot be reassigned',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'All three have the same scope and behavior',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'let and const are the same, var is different',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'var and let are the same, const is different',
            'is_correct' => false,
            'sort_order' => 4,
        ]);

        // React Question
        $question2 = Question::create([
            'title' => 'What are React Hooks and why were they introduced?',
            'question' => 'Explain what React Hooks are, why they were introduced, and provide examples of commonly used hooks.',
            'answer' => 'React Hooks are functions that allow you to use state and other React features in functional components. They were introduced to eliminate the need for class components and provide a more functional approach to React development.',
            'explanation' => 'Hooks like useState, useEffect, useContext allow functional components to have state and lifecycle methods. They make code more reusable and easier to test.',
            'type' => 'text',
            'difficulty' => 'intermediate',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        // CSS Question
        $question3 = Question::create([
            'title' => 'What is the CSS Box Model?',
            'question' => 'Explain the CSS Box Model and its components.',
            'answer' => 'The CSS Box Model consists of content, padding, border, and margin. It defines how the total width and height of an element is calculated.',
            'explanation' => 'Content is the actual content, padding is space around content, border surrounds padding, and margin is space outside border.',
            'type' => 'multiple_choice',
            'difficulty' => 'beginner',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Content, Padding, Border, Margin',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Width, Height, Color, Font',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Display, Position, Float, Clear',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Top, Right, Bottom, Left',
            'is_correct' => false,
            'sort_order' => 4,
        ]);
    }

    private function createBackendQuestions($admin, $category)
    {
        // PHP Question
        $question1 = Question::create([
            'title' => 'What is the difference between include and require in PHP?',
            'question' => 'Explain the differences between include, include_once, require, and require_once in PHP.',
            'answer' => 'include will produce a warning if file not found and continue execution, require will produce a fatal error and stop execution. The _once variants prevent multiple inclusions.',
            'explanation' => 'require is used for critical files, include for optional files. _once variants ensure files are included only once.',
            'type' => 'multiple_choice',
            'difficulty' => 'beginner',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'include produces warning, require produces fatal error',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'They are exactly the same',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'include is faster than require',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'require is deprecated',
            'is_correct' => false,
            'sort_order' => 4,
        ]);

        // Laravel Question
        $question2 = Question::create([
            'title' => 'What is Laravel Eloquent ORM?',
            'question' => 'Explain Laravel Eloquent ORM and its benefits.',
            'answer' => 'Eloquent is Laravel\'s built-in ORM that provides an ActiveRecord implementation for working with databases. It allows you to interact with database tables using PHP objects and methods.',
            'explanation' => 'Eloquent provides an elegant syntax for database operations, relationships, and query building while maintaining security and performance.',
            'type' => 'text',
            'difficulty' => 'intermediate',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        // Database Question
        $question3 = Question::create([
            'title' => 'What is database normalization?',
            'question' => 'Explain database normalization and its normal forms.',
            'answer' => 'Database normalization is the process of organizing data to reduce redundancy and improve data integrity. It involves dividing large tables into smaller, related tables.',
            'explanation' => 'Normal forms (1NF, 2NF, 3NF, BCNF) define rules for organizing data to eliminate redundancy and ensure data consistency.',
            'type' => 'multiple_choice',
            'difficulty' => 'advanced',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Process of organizing data to reduce redundancy',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Process of backing up database',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Process of indexing database',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Process of encrypting database',
            'is_correct' => false,
            'sort_order' => 4,
        ]);
    }

    private function createHRQuestions($admin, $category)
    {
        // Behavioral Question
        $question1 = Question::create([
            'title' => 'Tell me about a time you faced a challenging problem at work',
            'question' => 'Describe a challenging problem you encountered at work and how you solved it.',
            'answer' => 'This is a behavioral question that should be answered using the STAR method (Situation, Task, Action, Result).',
            'explanation' => 'Look for specific examples, problem-solving skills, and measurable results. The candidate should demonstrate analytical thinking and persistence.',
            'type' => 'text',
            'difficulty' => 'intermediate',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        // Leadership Question
        $question2 = Question::create([
            'title' => 'How do you handle conflicts in a team?',
            'question' => 'Describe your approach to resolving conflicts between team members.',
            'answer' => 'Effective conflict resolution involves listening to all parties, understanding root causes, facilitating open communication, and finding mutually acceptable solutions.',
            'explanation' => 'Look for emotional intelligence, communication skills, and ability to remain neutral while facilitating resolution.',
            'type' => 'multiple_choice',
            'difficulty' => 'intermediate',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);

        QuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Listen to all parties and facilitate open communication',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Ignore the conflict and hope it resolves itself',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Take sides with the person you like more',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Report to HR immediately without trying to resolve',
            'is_correct' => false,
            'sort_order' => 4,
        ]);

        // Motivation Question
        $question3 = Question::create([
            'title' => 'What motivates you in your work?',
            'question' => 'What factors motivate you to perform your best at work?',
            'answer' => 'Personal motivation varies, but good answers include learning opportunities, making an impact, solving challenging problems, working with great teams, and achieving goals.',
            'explanation' => 'Look for intrinsic motivation factors that align with the role and company culture.',
            'type' => 'text',
            'difficulty' => 'beginner',
            'category_id' => $category->id,
            'author_id' => $admin->id,
            'status' => true,
        ]);
    }
}
