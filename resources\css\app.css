/* Quill Editor Styles */
@import 'quill/dist/quill.snow.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Web Layout Navigation Styles */
.nav-link {
    @apply text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-link.active {
    @apply text-indigo-600 font-semibold;
}

.mobile-nav-link {
    @apply text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors;
}

.mobile-nav-link.active {
    @apply text-indigo-600 font-semibold;
}

.btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors;
}

/* Prose styles for content */
.prose {
    @apply text-gray-700 leading-relaxed;
}

.prose h1 {
    @apply text-3xl font-bold text-gray-900 mb-6;
}

.prose h2 {
    @apply text-2xl font-bold text-gray-900 mb-4 mt-8;
}

.prose h3 {
    @apply text-xl font-semibold text-gray-900 mb-3 mt-6;
}

.prose h4 {
    @apply text-lg font-semibold text-gray-900 mb-2 mt-4;
}

.prose p {
    @apply mb-4;
}

.prose ul, .prose ol {
    @apply mb-4 pl-6;
}

.prose li {
    @apply mb-2;
}

.prose a {
    @apply text-indigo-600 hover:text-indigo-800 underline;
}

.prose blockquote {
    @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
}

.prose code {
    @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
}

.prose pre {
    @apply bg-gray-100 p-4 rounded overflow-x-auto my-4;
}

.prose pre code {
    @apply bg-transparent p-0;
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom Quill Styling */
.ql-editor {
    min-height: 200px;
    font-size: 16px;
    line-height: 1.6;
}

.ql-toolbar {
    border-top: 1px solid #e5e7eb;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    border-bottom: none;
    border-radius: 0.5rem 0.5rem 0 0;
}

.ql-container {
    border-bottom: 1px solid #e5e7eb;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 0.5rem 0.5rem;
}

.ql-editor.ql-blank::before {
    color: #9ca3af;
    font-style: normal;
}

/* Dark mode support for Quill */
.dark .ql-toolbar {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .ql-container {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .ql-editor {
    color: #f9fafb;
}

.dark .ql-editor.ql-blank::before {
    color: #6b7280;
}

/* Enhanced form styling */
.form-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden;
}

.form-section {
    @apply p-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0;
}

.form-section-header {
    @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.form-grid {
    @apply grid grid-cols-1 gap-6;
}

.form-group {
    @apply space-y-2;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input {
    @apply block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
}

.form-select {
    @apply block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
}

.form-textarea {
    @apply block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
}

.btn-primary {
    @apply inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-success {
    @apply inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-danger {
    @apply inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-outline {
    @apply inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150;
}

/* Card components */
.stats-card {
    @apply bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200;
}

.content-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-all duration-200;
}

/* Loading states */
.loading-spinner {
    @apply inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-current;
}

/* Enhanced table styling */
.data-table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.data-table th {
    @apply px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
}

.data-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}
