<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'category',
        'slug',
        'parent_category',
        'description',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Self-referencing relationship for parent category
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_category');
    }

    // Self-referencing relationship for child categories
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_category')->orderBy('sort_order');
    }

    // Get all descendants (children, grandchildren, etc.)
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    // Get all ancestors (parent, grandparent, etc.)
    public function ancestors()
    {
        $ancestors = collect();
        $parent = $this->parent;

        while ($parent) {
            $ancestors->push($parent);
            $parent = $parent->parent;
        }

        return $ancestors->reverse();
    }

    // Get the full path (breadcrumb) of the category
    public function getFullPathAttribute()
    {
        $path = $this->ancestors()->pluck('category')->toArray();
        $path[] = $this->category;
        return implode(' > ', $path);
    }

    // Get depth level of the category
    public function getDepthAttribute()
    {
        return $this->ancestors()->count();
    }

    // One-to-many relationship with pages
    public function pages()
    {
        return $this->hasMany(Page::class);
    }

    // Scope for root categories (no parent)
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_category');
    }

    // Scope for active categories
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    // Check if category has children
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    // Get route key name
    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Auto-generate slug when creating
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->category);
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('category') && empty($category->slug)) {
                $category->slug = Str::slug($category->category);
            }
        });
    }
}
