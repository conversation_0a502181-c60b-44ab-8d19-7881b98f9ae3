<?php

namespace App\Http\Controllers;

use App\Models\Quiz;
use App\Models\Category;
use App\Models\Question;
use App\Models\QuizAttempt;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class QuizController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:manage-quizzes')->except(['index', 'show', 'take', 'submit']);
        $this->middleware('permission:take-quizzes')->only(['take', 'submit']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $quizzes = Quiz::with(['category', 'author'])
            ->active()
            ->paginate(12);

        $categories = Category::active()->get();

        return view('backend.quizzes.index', compact('quizzes', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->get();
        $questions = Question::active()->with('category')->get();

        return view('backend.quizzes.create', compact('categories', 'questions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
            'passing_score' => 'nullable|integer|min:0|max:100',
            'shuffle_questions' => 'boolean',
            'shuffle_options' => 'boolean',
            'show_results_immediately' => 'boolean',
            'status' => 'boolean',
            'questions' => 'array',
            'questions.*' => 'exists:questions,id',
            'points' => 'array',
            'points.*' => 'integer|min:1',
        ]);

        $quiz = Quiz::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'description' => $request->description,
            'category_id' => $request->category_id,
            'author_id' => auth()->id(),
            'time_limit' => $request->time_limit,
            'max_attempts' => $request->max_attempts ?? 3,
            'passing_score' => $request->passing_score ?? 70,
            'shuffle_questions' => $request->boolean('shuffle_questions'),
            'shuffle_options' => $request->boolean('shuffle_options'),
            'show_results_immediately' => $request->boolean('show_results_immediately'),
            'status' => $request->boolean('status'),
        ]);

        // Attach questions with points
        if ($request->questions) {
            $questionsData = [];
            foreach ($request->questions as $index => $questionId) {
                $questionsData[$questionId] = [
                    'sort_order' => $index + 1,
                    'points' => $request->points[$index] ?? 1,
                ];
            }
            $quiz->questions()->attach($questionsData);
        }

        return redirect()->route('quizzes.index')
            ->with('success', 'Quiz created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Quiz $quiz)
    {
        $quiz->load(['category', 'author', 'questions']);

        $userAttempts = null;
        $bestScore = null;
        $canAttempt = true;

        if (auth()->check()) {
            $userAttempts = $quiz->getUserAttempts(auth()->id());
            $bestScore = $quiz->getUserBestScore(auth()->id());
            $canAttempt = $quiz->canUserAttempt(auth()->id());
        }

        return view('backend.quizzes.show', compact('quiz', 'userAttempts', 'bestScore', 'canAttempt'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Quiz $quiz)
    {
        $quiz->load(['questions']);
        $categories = Category::active()->get();
        $questions = Question::active()->with('category')->get();

        return view('backend.quizzes.edit', compact('quiz', 'categories', 'questions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Quiz $quiz)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
            'passing_score' => 'nullable|integer|min:0|max:100',
            'shuffle_questions' => 'boolean',
            'shuffle_options' => 'boolean',
            'show_results_immediately' => 'boolean',
            'status' => 'boolean',
            'questions' => 'array',
            'questions.*' => 'exists:questions,id',
            'points' => 'array',
            'points.*' => 'integer|min:1',
        ]);

        $quiz->update([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'description' => $request->description,
            'category_id' => $request->category_id,
            'time_limit' => $request->time_limit,
            'max_attempts' => $request->max_attempts ?? 3,
            'passing_score' => $request->passing_score ?? 70,
            'shuffle_questions' => $request->boolean('shuffle_questions'),
            'shuffle_options' => $request->boolean('shuffle_options'),
            'show_results_immediately' => $request->boolean('show_results_immediately'),
            'status' => $request->boolean('status'),
        ]);

        // Sync questions with points
        if ($request->questions) {
            $questionsData = [];
            foreach ($request->questions as $index => $questionId) {
                $questionsData[$questionId] = [
                    'sort_order' => $index + 1,
                    'points' => $request->points[$index] ?? 1,
                ];
            }
            $quiz->questions()->sync($questionsData);
        } else {
            $quiz->questions()->detach();
        }

        return redirect()->route('quizzes.index')
            ->with('success', 'Quiz updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Quiz $quiz)
    {
        $quiz->delete();

        return redirect()->route('quizzes.index')
            ->with('success', 'Quiz deleted successfully.');
    }
}
