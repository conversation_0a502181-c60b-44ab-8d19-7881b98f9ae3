<?php $__env->startSection('title', 'Interview Questions - Qualifyrs'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Interview Questions</h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Prepare for your next job interview with our comprehensive collection of interview questions across various technologies and roles.
                </p>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <form action="<?php echo e(route('interview.search')); ?>" method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="q" value="<?php echo e(request('search')); ?>" 
                           placeholder="Search interview questions..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Search
                </button>
            </form>
        </div>

        <!-- Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('interview.category', $category->slug)); ?>" class="hover:text-blue-600">
                                <?php echo e($category->category); ?>

                            </a>
                        </h3>
                        <p class="text-gray-600 mb-4"><?php echo e($category->description); ?></p>
                        
                        <?php if($category->children->count() > 0): ?>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $category->children->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(route('interview.category', $child->slug)); ?>" 
                                       class="block text-sm text-blue-600 hover:text-blue-800">
                                        <?php echo e($child->category); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($category->children->count() > 3): ?>
                                    <p class="text-sm text-gray-500">+<?php echo e($category->children->count() - 3); ?> more</p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Difficulty Levels -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Browse by Difficulty</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="<?php echo e(route('interview.difficulty', 'beginner')); ?>" 
                   class="p-4 text-center bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <div class="text-green-600 font-semibold">Beginner</div>
                    <div class="text-sm text-green-500">Entry Level</div>
                </a>
                <a href="<?php echo e(route('interview.difficulty', 'intermediate')); ?>" 
                   class="p-4 text-center bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                    <div class="text-yellow-600 font-semibold">Intermediate</div>
                    <div class="text-sm text-yellow-500">Mid Level</div>
                </a>
                <a href="<?php echo e(route('interview.difficulty', 'advanced')); ?>" 
                   class="p-4 text-center bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <div class="text-orange-600 font-semibold">Advanced</div>
                    <div class="text-sm text-orange-500">Senior Level</div>
                </a>
                <a href="<?php echo e(route('interview.difficulty', 'expert')); ?>" 
                   class="p-4 text-center bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <div class="text-red-600 font-semibold">Expert</div>
                    <div class="text-sm text-red-500">Lead Level</div>
                </a>
            </div>
        </div>

        <!-- Recent Questions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Questions</h2>
            <div class="space-y-6">
                <?php $__empty_1 = true; $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="<?php echo e(route('interview.show', $question->id)); ?>" class="hover:text-blue-600">
                                        <?php echo e($question->title); ?>

                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3"><?php echo e(Str::limit($question->question, 200)); ?></p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo e($question->category->category); ?>

                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        <?php if($question->difficulty === 'beginner'): ?> bg-green-100 text-green-800
                                        <?php elseif($question->difficulty === 'intermediate'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($question->difficulty === 'advanced'): ?> bg-orange-100 text-orange-800
                                        <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($question->difficulty)); ?>

                                    </span>
                                    <span><?php echo e($question->views); ?> views</span>
                                    <span><?php echo e($question->created_at->diffForHumans()); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg">No questions found.</div>
                        <p class="text-gray-400 mt-2">Check back later for new interview questions.</p>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($questions->hasPages()): ?>
                <div class="mt-8">
                    <?php echo e($questions->links()); ?>

                </div>
            <?php endif; ?>
        </div>

        <!-- Popular Tags -->
        <?php if($popularTags->count() > 0): ?>
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Popular Tags</h2>
                <div class="flex flex-wrap gap-2">
                    <?php $__currentLoopData = $popularTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(route('interview.tag', $tag->slug)); ?>" 
                           class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white hover:opacity-80 transition-opacity"
                           style="background-color: <?php echo e($tag->color); ?>">
                            <?php echo e($tag->name); ?>

                            <span class="ml-1 text-xs opacity-75">(<?php echo e($tag->usage_count); ?>)</span>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\qualifyrs\resources\views/interview/index.blade.php ENDPATH**/ ?>