<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo e($page->title); ?> - <?php echo e(config('app.name', 'Laravel')); ?></title>
    <meta name="description" content="<?php echo e($page->meta_description ?: Str::limit(strip_tags($page->content), 160)); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo e($page->title); ?>">
    <meta property="og:description" content="<?php echo e($page->meta_description ?: Str::limit(strip_tags($page->content), 160)); ?>">
    <meta property="og:type" content="article">
    <meta property="og:url" content="<?php echo e(request()->url()); ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900"><?php echo e(config('app.name', 'Laravel')); ?></a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('pages.index')); ?>" class="text-gray-700 hover:text-gray-900">All Pages</a>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-700 hover:text-gray-900">Dashboard</a>
                        <?php if(auth()->user()->hasAnyRole(['admin', 'editor']) && (auth()->user()->hasRole('admin') || auth()->user()->id === $page->author_id)): ?>
                            <a href="<?php echo e(route('pages.edit', $page)); ?>" class="text-indigo-600 hover:text-indigo-800">Edit Page</a>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="text-gray-700 hover:text-gray-900">Logout</button>
                        </form>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-700 hover:text-gray-900">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav class="bg-white border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
                <a href="/" class="hover:text-gray-700">Home</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="<?php echo e(route('pages.index')); ?>" class="hover:text-gray-700">Pages</a>
                <?php if($page->category): ?>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <a href="<?php echo e(route('pages.category', $page->category->slug)); ?>" class="hover:text-gray-700"><?php echo e($page->category->category); ?></a>
                <?php endif; ?>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900"><?php echo e(Str::limit($page->title, 50)); ?></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Article Header -->
            <header class="p-8 border-b border-gray-200">
                <div class="flex items-center justify-between mb-6">
                    <?php if($page->category): ?>
                        <a href="<?php echo e(route('pages.category', $page->category->slug)); ?>" 
                           class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
                            <?php echo e($page->category->category); ?>

                        </a>
                    <?php endif; ?>
                    
                    <div class="flex items-center space-x-2">
                        <?php if($page->featured): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Featured
                            </span>
                        <?php endif; ?>
                        <?php if(!$page->status): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Draft
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <h1 class="text-4xl font-bold text-gray-900 mb-6 leading-tight"><?php echo e($page->title); ?></h1>

                <div class="flex items-center justify-between text-sm text-gray-600 mb-6">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                <span class="text-xs font-medium text-gray-700">
                                    <?php echo e(strtoupper(substr($page->author->name, 0, 2))); ?>

                                </span>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900"><?php echo e($page->author->name); ?></div>
                                <div class="text-xs text-gray-500">Author</div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm0 0v4a2 2 0 002 2h6a2 2 0 002-2v-4a2 2 0 00-2-2H10a2 2 0 00-2 2z"></path>
                            </svg>
                            <div>
                                <div class="font-medium"><?php echo e($page->created_at->format('F j, Y')); ?></div>
                                <div class="text-xs text-gray-500">Published</div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <div>
                                <div class="font-medium"><?php echo e($page->views); ?> views</div>
                                <div class="text-xs text-gray-500">Total</div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($page->updated_at != $page->created_at): ?>
                        <div class="text-xs text-gray-500">
                            Updated <?php echo e($page->updated_at->format('M j, Y')); ?>

                        </div>
                    <?php endif; ?>
                </div>

                <?php if($page->tags->count() > 0): ?>
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $page->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('pages.tag', $tag->slug)); ?>" 
                               class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium hover:opacity-80 transition-opacity"
                               style="background-color: <?php echo e($tag->color); ?>20; color: <?php echo e($tag->color); ?>;">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                <?php echo e($tag->name); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </header>

            <!-- Article Content -->
            <div class="p-8">
                <div class="prose prose-lg max-w-none">
                    <?php echo $page->content; ?>

                </div>
            </div>

            <!-- Article Footer -->
            <footer class="p-8 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Share this page:</span>
                        <div class="flex space-x-3">
                            <a href="#" class="text-blue-600 hover:text-blue-800" title="Share on Twitter">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-blue-600 hover:text-blue-800" title="Share on Facebook">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-blue-600 hover:text-blue-800" title="Share on LinkedIn">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-500">
                        <a href="<?php echo e(route('pages.index')); ?>" class="hover:text-gray-700">← Back to all pages</a>
                    </div>
                </div>
            </footer>
        </article>

        <!-- Related Pages -->
        <?php if($page->category && $page->category->pages->where('id', '!=', $page->id)->count() > 0): ?>
            <section class="mt-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">More from <?php echo e($page->category->category); ?></h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $page->category->pages->where('id', '!=', $page->id)->where('status', true)->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedPage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('pages.show', [$relatedPage->category->slug, $relatedPage->slug])); ?>" 
                                       class="hover:text-indigo-600">
                                        <?php echo e($relatedPage->title); ?>

                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                    <?php echo e(Str::limit(strip_tags($relatedPage->content), 100)); ?>

                                </p>
                                
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span><?php echo e($relatedPage->author->name); ?></span>
                                    <span><?php echo e($relatedPage->created_at->format('M j, Y')); ?></span>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500 text-sm">
                © <?php echo e(date('Y')); ?> <?php echo e(config('app.name', 'Laravel')); ?>. All rights reserved.
            </div>
        </div>
    </footer>
</body>
</html>
<?php /**PATH C:\laragon\www\qualifyrs\resources\views/pages/show.blade.php ENDPATH**/ ?>