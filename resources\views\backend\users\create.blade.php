<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New User') }}
            </h2>
            <a href="{{ route('users.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Back to Users
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('users.store') }}" method="POST">
                        @csrf

                        <div class="space-y-6">
                            <!-- Name -->
                            <div>
                                <x-input-label for="name" :value="__('Full Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" 
                                              :value="old('name')" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <!-- Email -->
                            <div>
                                <x-input-label for="email" :value="__('Email Address')" />
                                <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" 
                                              :value="old('email')" required />
                                <x-input-error class="mt-2" :messages="$errors->get('email')" />
                            </div>

                            <!-- Password -->
                            <div>
                                <x-input-label for="password" :value="__('Password')" />
                                <x-text-input id="password" name="password" type="password" class="mt-1 block w-full" 
                                              required />
                                <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters long.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('password')" />
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
                                <x-text-input id="password_confirmation" name="password_confirmation" type="password" class="mt-1 block w-full" 
                                              required />
                                <x-input-error class="mt-2" :messages="$errors->get('password_confirmation')" />
                            </div>

                            <!-- Roles -->
                            <div>
                                <x-input-label for="roles" :value="__('User Roles')" />
                                <div class="mt-2 space-y-2">
                                    @foreach($roles as $role)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="roles[]" value="{{ $role->name }}" 
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                   {{ in_array($role->name, old('roles', [])) ? 'checked' : '' }}>
                                            <span class="ml-2 text-sm text-gray-700">
                                                <span class="font-medium">{{ ucfirst($role->name) }}</span>
                                                @if($role->name === 'admin')
                                                    <span class="text-gray-500">- Full system access</span>
                                                @elseif($role->name === 'editor')
                                                    <span class="text-gray-500">- Can create and edit content</span>
                                                @else
                                                    <span class="text-gray-500">- Basic user access</span>
                                                @endif
                                            </span>
                                        </label>
                                    @endforeach
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Select one or more roles for this user.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('roles')" />
                            </div>

                            <!-- Email Verification -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Email Verification</h3>
                                <div class="flex items-center">
                                    <input id="email_verified" name="email_verified" type="checkbox" value="1" 
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                           {{ old('email_verified') ? 'checked' : '' }}>
                                    <label for="email_verified" class="ml-2 block text-sm text-gray-700">
                                        Mark email as verified
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">If unchecked, the user will need to verify their email address.</p>
                            </div>

                            <!-- Account Settings -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Account Settings</h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input id="send_welcome_email" name="send_welcome_email" type="checkbox" value="1" 
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                               {{ old('send_welcome_email', true) ? 'checked' : '' }}>
                                        <label for="send_welcome_email" class="ml-2 block text-sm text-gray-700">
                                            Send welcome email to user
                                        </label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="force_password_change" name="force_password_change" type="checkbox" value="1" 
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                               {{ old('force_password_change') ? 'checked' : '' }}>
                                        <label for="force_password_change" class="ml-2 block text-sm text-gray-700">
                                            Require password change on first login
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Create User') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
