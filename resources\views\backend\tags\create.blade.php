<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Tag') }}
            </h2>
            <a href="{{ route('tags.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Back to Tags
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('tags.store') }}" method="POST" x-data="tagForm()">
                        @csrf

                        <div class="space-y-6">
                            <!-- Tag Name -->
                            <div>
                                <x-input-label for="name" :value="__('Tag Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" 
                                              :value="old('name')" required autofocus 
                                              x-model="name" @input="generateSlug" />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <!-- Slug -->
                            <div>
                                <x-input-label for="slug" :value="__('Slug')" />
                                <x-text-input id="slug" name="slug" type="text" class="mt-1 block w-full" 
                                              :value="old('slug')" x-model="slug" />
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the tag name. Leave blank to auto-generate.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('slug')" />
                            </div>

                            <!-- Color -->
                            <div>
                                <x-input-label for="color" :value="__('Color')" />
                                <div class="mt-1 flex items-center space-x-4">
                                    <input id="color" name="color" type="color" 
                                           class="h-10 w-20 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" 
                                           :value="old('color', '#3B82F6')" x-model="color" @input="updatePreview">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600">Preview:</span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                              :style="`background-color: ${color}20; color: ${color};`"
                                              x-text="name || 'Sample Tag'">
                                        </span>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Choose a color that represents this tag.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('color')" />
                            </div>

                            <!-- Predefined Colors -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Colors</label>
                                <div class="grid grid-cols-8 gap-2">
                                    <template x-for="presetColor in presetColors" :key="presetColor">
                                        <button type="button" 
                                                class="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                :style="`background-color: ${presetColor}`"
                                                @click="color = presetColor; updatePreview()"
                                                :class="{ 'ring-2 ring-indigo-500': color === presetColor }">
                                        </button>
                                    </template>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <x-input-label for="description" :value="__('Description')" />
                                <textarea id="description" name="description" rows="3" 
                                          class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                          placeholder="Brief description of this tag...">{{ old('description') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                            </div>

                            <!-- Status -->
                            <div class="flex items-center">
                                <input id="status" name="status" type="checkbox" value="1" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                       {{ old('status', true) ? 'checked' : '' }}>
                                <label for="status" class="ml-2 block text-sm text-gray-900">
                                    Active (available for use)
                                </label>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Create Tag') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function tagForm() {
            return {
                name: '',
                slug: '',
                color: '#3B82F6',
                presetColors: [
                    '#EF4444', '#F97316', '#F59E0B', '#EAB308',
                    '#84CC16', '#22C55E', '#10B981', '#14B8A6',
                    '#06B6D4', '#0EA5E9', '#3B82F6', '#6366F1',
                    '#8B5CF6', '#A855F7', '#D946EF', '#EC4899'
                ],
                
                generateSlug() {
                    if (this.name && !this.slug) {
                        this.slug = this.name.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                },
                
                updatePreview() {
                    // This method is called when color changes to update the preview
                }
            }
        }
    </script>
</x-app-layout>
