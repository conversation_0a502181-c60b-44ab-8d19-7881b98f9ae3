<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'status',
        'usage_count'
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Many-to-many relationship with pages
    public function pages()
    {
        return $this->belongsToMany(Page::class);
    }

    // Scope for active tags
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    // Scope for popular tags (most used)
    public function scopePopular($query, $limit = 10)
    {
        return $query->where('usage_count', '>', 0)
                    ->orderBy('usage_count', 'desc')
                    ->limit($limit);
    }

    // Get route key name
    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Update usage count when pages are attached/detached
    public function updateUsageCount()
    {
        $this->update(['usage_count' => $this->pages()->count()]);
    }

    // Auto-generate slug when creating
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });

        static::updating(function ($tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });
    }
}
