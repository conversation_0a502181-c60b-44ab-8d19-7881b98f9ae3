<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TagController extends Controller
{
    public function __construct()
    {
    }

    public function index()
    {
        $tags = Tag::withCount('pages')
                   ->orderBy('usage_count', 'desc')
                   ->paginate(15);

        return view('tags.index', compact('tags'));
    }

    public function create()
    {
        return view('tags.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:tags',
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'status' => 'boolean'
        ]);

        $slug = $request->slug ?: Str::slug($request->name);

        // Ensure unique slug
        $originalSlug = $slug;
        $counter = 1;
        while (Tag::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        Tag::create([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'color' => $request->color,
            'status' => $request->has('status'),
        ]);

        return redirect()->route('tags.index')
            ->with('success', 'Tag created successfully.');
    }

    public function show(Tag $tag)
    {
        $tag->load('pages');
        return view('tags.show', compact('tag'));
    }

    public function edit(Tag $tag)
    {
        return view('tags.edit', compact('tag'));
    }

    public function update(Request $request, Tag $tag)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:tags,slug,' . $tag->id,
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'status' => 'boolean'
        ]);

        $data = $request->all();
        $data['status'] = $request->has('status');

        if ($request->name !== $tag->name && !$request->slug) {
            $slug = Str::slug($request->name);
            $originalSlug = $slug;
            $counter = 1;
            while (Tag::where('slug', $slug)->where('id', '!=', $tag->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $data['slug'] = $slug;
        }

        $tag->update($data);

        return redirect()->route('tags.index')
            ->with('success', 'Tag updated successfully.');
    }

    public function destroy(Tag $tag)
    {
        // Check if tag has pages
        if ($tag->pages()->count() > 0) {
            return redirect()->route('tags.index')
                ->with('error', 'Cannot delete tag with pages. Please remove tag from pages first.');
        }

        $tag->delete();

        return redirect()->route('tags.index')
            ->with('success', 'Tag deleted successfully.');
    }

    // API method to search tags for AJAX requests
    public function search(Request $request)
    {
        $query = $request->get('q');

        $tags = Tag::where('name', 'LIKE', "%{$query}%")
                   ->active()
                   ->limit(10)
                   ->get(['id', 'name', 'color']);

        return response()->json($tags);
    }
}
