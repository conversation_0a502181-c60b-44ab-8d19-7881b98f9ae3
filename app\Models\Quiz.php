<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Quiz extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'category_id',
        'author_id',
        'time_limit',
        'max_attempts',
        'passing_score',
        'shuffle_questions',
        'shuffle_options',
        'show_results_immediately',
        'status',
        'settings'
    ];

    protected $casts = [
        'shuffle_questions' => 'boolean',
        'shuffle_options' => 'boolean',
        'show_results_immediately' => 'boolean',
        'status' => 'boolean',
        'settings' => 'array',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function questions()
    {
        return $this->belongsToMany(Question::class)->withPivot('sort_order', 'points')->withTimestamps()->orderBy('pivot_sort_order');
    }

    public function attempts()
    {
        return $this->hasMany(QuizAttempt::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getTotalQuestions()
    {
        return $this->questions()->count();
    }

    public function getMaxScore()
    {
        return $this->questions()->sum('pivot_points');
    }

    public function getUserAttempts($userId)
    {
        return $this->attempts()->where('user_id', $userId)->count();
    }

    public function canUserAttempt($userId)
    {
        return $this->getUserAttempts($userId) < $this->max_attempts;
    }

    public function getUserBestScore($userId)
    {
        return $this->attempts()
            ->where('user_id', $userId)
            ->where('completed_at', '!=', null)
            ->max('percentage');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quiz) {
            if (empty($quiz->slug)) {
                $quiz->slug = Str::slug($quiz->title);
            }
        });

        static::updating(function ($quiz) {
            if ($quiz->isDirty('title') && empty($quiz->slug)) {
                $quiz->slug = Str::slug($quiz->title);
            }
        });
    }
}
