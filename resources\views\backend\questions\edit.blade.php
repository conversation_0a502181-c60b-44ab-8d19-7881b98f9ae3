<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ __('Edit Question') }}
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Update the question: {{ $question->title }}
                </p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('questions.show', $question) }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Question
                </a>
                <a href="{{ route('questions.index') }}" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Questions
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="px-4 sm:px-6 lg:px-8">
            <form action="{{ route('questions.update', $question) }}" method="POST" x-data="questionForm()">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Basic Information -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Basic Information</h3>
                                <div class="form-grid">
                                    <!-- Title -->
                                    <div class="form-group">
                                        <label for="title" class="form-label">Question Title *</label>
                                        <input type="text" 
                                               id="title" 
                                               name="title" 
                                               value="{{ old('title', $question->title) }}"
                                               class="form-input" 
                                               required 
                                               autofocus
                                               placeholder="Enter a descriptive title for the question">
                                        @error('title')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Question Content -->
                                    <div class="form-group">
                                        <label for="question" class="form-label">Question Content *</label>
                                        <x-quill-editor 
                                            name="question" 
                                            :value="old('question', $question->question)" 
                                            placeholder="Write your question here..."
                                            height="250px"
                                            required />
                                    </div>

                                    <!-- Answer (for non-multiple choice) -->
                                    <div class="form-group" x-show="questionType !== 'multiple_choice'">
                                        <label for="answer" class="form-label">Answer</label>
                                        <x-quill-editor 
                                            name="answer" 
                                            :value="old('answer', $question->answer)" 
                                            placeholder="Provide the answer or solution..."
                                            height="200px" />
                                    </div>

                                    <!-- Explanation -->
                                    <div class="form-group">
                                        <label for="explanation" class="form-label">Explanation</label>
                                        <x-quill-editor 
                                            name="explanation" 
                                            :value="old('explanation', $question->explanation)" 
                                            placeholder="Explain the answer or provide additional context..."
                                            height="200px" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Multiple Choice Options -->
                        <div class="form-card" x-show="questionType === 'multiple_choice'">
                            <div class="form-section">
                                <h3 class="form-section-header">Answer Options</h3>
                                <div class="space-y-4">
                                    <template x-for="(option, index) in options" :key="index">
                                        <div class="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                                            <input type="checkbox" 
                                                   :name="'options[' + index + '][is_correct]'"
                                                   x-model="option.is_correct"
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <input type="text" 
                                                   :name="'options[' + index + '][text]'"
                                                   x-model="option.text"
                                                   placeholder="Enter option text"
                                                   class="flex-1 form-input">
                                            <button type="button" 
                                                    @click="removeOption(index)"
                                                    x-show="options.length > 2"
                                                    class="text-red-600 hover:text-red-800">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </template>
                                    
                                    <button type="button" 
                                            @click="addOption()"
                                            class="w-full p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                                        <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add Option
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Question Settings -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Question Settings</h3>
                                <div class="form-grid">
                                    <!-- Type -->
                                    <div class="form-group">
                                        <label for="type" class="form-label">Question Type *</label>
                                        <select id="type" 
                                                name="type" 
                                                x-model="questionType"
                                                class="form-select" 
                                                required>
                                            @foreach($types as $type)
                                                <option value="{{ $type }}" {{ old('type', $question->type) === $type ? 'selected' : '' }}>
                                                    {{ ucwords(str_replace('_', ' ', $type)) }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('type')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Difficulty -->
                                    <div class="form-group">
                                        <label for="difficulty" class="form-label">Difficulty Level *</label>
                                        <select id="difficulty" 
                                                name="difficulty" 
                                                class="form-select" 
                                                required>
                                            @foreach($difficulties as $difficulty)
                                                <option value="{{ $difficulty }}" {{ old('difficulty', $question->difficulty) === $difficulty ? 'selected' : '' }}>
                                                    {{ ucfirst($difficulty) }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('difficulty')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Category -->
                                    <div class="form-group">
                                        <label for="category_id" class="form-label">Category *</label>
                                        <select id="category_id" 
                                                name="category_id" 
                                                class="form-select" 
                                                required>
                                            <option value="">Select Category</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}" {{ old('category_id', $question->category_id) == $category->id ? 'selected' : '' }}>
                                                    {{ $category->category }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('category_id')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tags -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Tags</h3>
                                <div class="space-y-2 max-h-48 overflow-y-auto">
                                    @foreach($tags as $tag)
                                        <label class="flex items-center">
                                            <input type="checkbox" 
                                                   name="tags[]" 
                                                   value="{{ $tag->id }}"
                                                   {{ in_array($tag->id, old('tags', $question->tags->pluck('id')->toArray())) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $tag->name }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="form-card">
                            <div class="form-section">
                                <div class="flex flex-col space-y-3">
                                    <button type="submit" class="btn-primary w-full">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Update Question
                                    </button>
                                    <a href="{{ route('questions.show', $question) }}" class="btn-outline w-full text-center">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        function questionForm() {
            return {
                questionType: '{{ old('type', $question->type) }}',
                options: @json($question->options->map(function($option) {
                    return [
                        'text' => $option->option_text,
                        'is_correct' => $option->is_correct
                    ];
                })->values()),
                
                init() {
                    // Ensure we have at least 2 options for multiple choice
                    if (this.questionType === 'multiple_choice' && this.options.length < 2) {
                        while (this.options.length < 2) {
                            this.options.push({ text: '', is_correct: false });
                        }
                    }
                },
                
                addOption() {
                    this.options.push({ text: '', is_correct: false });
                },
                
                removeOption(index) {
                    if (this.options.length > 2) {
                        this.options.splice(index, 1);
                    }
                }
            }
        }
    </script>
</x-app-layout>
