import './bootstrap';

import Alpine from 'alpinejs';
import Quill from 'quill';

window.Alpine = Alpine;
window.Quill = Quill;

// Quill configuration
window.initQuill = function(selector, options = {}) {
    const defaultOptions = {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                [{ 'font': [] }],
                [{ 'size': ['small', false, 'large', 'huge'] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                [{ 'direction': 'rtl' }],
                [{ 'align': [] }],
                ['blockquote', 'code-block'],
                ['link', 'image', 'video'],
                ['clean']
            ]
        },
        placeholder: 'Start writing...',
        ...options
    };

    const quill = new Quill(selector, defaultOptions);

    // Handle form submission
    const form = document.querySelector(selector).closest('form');
    if (form) {
        form.addEventListener('submit', function() {
            const hiddenInput = form.querySelector('input[name="' + selector.replace('#', '') + '"]') ||
                               form.querySelector('textarea[name="' + selector.replace('#', '') + '"]');
            if (hiddenInput) {
                hiddenInput.value = quill.root.innerHTML;
            }
        });
    }

    return quill;
};

Alpine.start();
