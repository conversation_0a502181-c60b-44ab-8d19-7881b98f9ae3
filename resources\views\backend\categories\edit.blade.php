<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Category: ') . $category->category }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('categories.show', $category) }}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    View Category
                </a>
                <a href="{{ route('categories.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Categories
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('categories.update', $category) }}" method="POST" x-data="categoryForm()">
                        @csrf
                        @method('PUT')

                        <div class="space-y-6">
                            <!-- Category Information -->
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Category Information</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div><strong>Created:</strong> {{ $category->created_at->format('M j, Y g:i A') }}</div>
                                    <div><strong>Updated:</strong> {{ $category->updated_at->format('M j, Y g:i A') }}</div>
                                    <div><strong>Pages:</strong> {{ $category->pages->count() }}</div>
                                    <div><strong>Subcategories:</strong> {{ $category->children->count() }}</div>
                                </div>
                            </div>

                            <!-- Category Name -->
                            <div>
                                <x-input-label for="category" :value="__('Category Name')" />
                                <x-text-input id="category" name="category" type="text" class="mt-1 block w-full" 
                                              :value="old('category', $category->category)" required autofocus 
                                              x-model="categoryName" @input="generateSlug" />
                                <x-input-error class="mt-2" :messages="$errors->get('category')" />
                            </div>

                            <!-- Slug -->
                            <div>
                                <x-input-label for="slug" :value="__('Slug')" />
                                <x-text-input id="slug" name="slug" type="text" class="mt-1 block w-full" 
                                              :value="old('slug', $category->slug)" x-model="slug" />
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the category name.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('slug')" />
                            </div>

                            <!-- Parent Category -->
                            <div>
                                <x-input-label for="parent_category" :value="__('Parent Category')" />
                                <select id="parent_category" name="parent_category" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                    <option value="">None (Root Category)</option>
                                    @foreach($parentCategories as $parentCategory)
                                        @if($parentCategory->id !== $category->id)
                                            <option value="{{ $parentCategory->id }}" 
                                                    {{ old('parent_category', $category->parent_category) == $parentCategory->id ? 'selected' : '' }}>
                                                {{ $parentCategory->category }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                                <p class="mt-1 text-sm text-gray-500">Select a parent category to create a subcategory.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('parent_category')" />
                            </div>

                            <!-- Description -->
                            <div>
                                <x-input-label for="description" :value="__('Description')" />
                                <textarea id="description" name="description" rows="4" 
                                          class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                          placeholder="Brief description of this category...">{{ old('description', $category->description) }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <x-input-label for="sort_order" :value="__('Sort Order')" />
                                <x-text-input id="sort_order" name="sort_order" type="number" class="mt-1 block w-full" 
                                              :value="old('sort_order', $category->sort_order)" min="0" />
                                <p class="mt-1 text-sm text-gray-500">Lower numbers appear first.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('sort_order')" />
                            </div>

                            <!-- Status -->
                            <div class="flex items-center">
                                <input id="status" name="status" type="checkbox" value="1" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                       {{ old('status', $category->status) ? 'checked' : '' }}>
                                <label for="status" class="ml-2 block text-sm text-gray-900">
                                    Active (visible to users)
                                </label>
                            </div>

                            <!-- Warning Messages -->
                            @if($category->children->count() > 0)
                                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">
                                                This category has {{ $category->children->count() }} subcategories
                                            </h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p>Changing the parent category will affect the hierarchy structure.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($category->pages->count() > 0)
                                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-blue-800">
                                                This category contains {{ $category->pages->count() }} pages
                                            </h3>
                                            <div class="mt-2 text-sm text-blue-700">
                                                <p>Deactivating this category will hide it from public view but won't affect the pages.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Update Category') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function categoryForm() {
            return {
                categoryName: '{{ old('category', $category->category) }}',
                slug: '{{ old('slug', $category->slug) }}',
                generateSlug() {
                    // Only auto-generate if slug is empty
                    if (this.categoryName && !this.slug) {
                        this.slug = this.categoryName.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                }
            }
        }
    </script>
</x-app-layout>
