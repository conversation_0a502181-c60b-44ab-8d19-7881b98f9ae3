<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('question');
            $table->text('answer')->nullable();
            $table->text('explanation')->nullable();
            $table->enum('type', ['multiple_choice', 'true_false', 'text', 'coding'])->default('multiple_choice');
            $table->enum('difficulty', ['beginner', 'intermediate', 'advanced', 'expert'])->default('beginner');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('author_id')->constrained('users')->onDelete('cascade');
            $table->boolean('status')->default(true);
            $table->integer('views')->default(0);
            $table->integer('usage_count')->default(0); // How many times used in quizzes
            $table->json('metadata')->nullable(); // For additional data like time limit, points, etc.
            $table->timestamps();

            $table->index(['category_id', 'status']);
            $table->index(['difficulty', 'status']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
