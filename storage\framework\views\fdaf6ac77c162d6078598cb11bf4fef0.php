<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'content',
    'value' => '',
    'placeholder' => 'Start writing...',
    'height' => '300px',
    'required' => false,
    'id' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'content',
    'value' => '',
    'placeholder' => 'Start writing...',
    'height' => '300px',
    'required' => false,
    'id' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $editorId = $id ?? 'quill-editor-' . Str::random(8);
    $hiddenInputId = $name . '-hidden';
?>

<div class="quill-editor-wrapper" x-data="quillEditor('<?php echo e($editorId); ?>', '<?php echo e($hiddenInputId); ?>', '<?php echo e($placeholder); ?>')">
    <!-- Quill Editor Container -->
    <div id="<?php echo e($editorId); ?>" style="min-height: <?php echo e($height); ?>"></div>
    
    <!-- Hidden Input for Form Submission -->
    <input type="hidden" 
           name="<?php echo e($name); ?>" 
           id="<?php echo e($hiddenInputId); ?>"
           value="<?php echo e(old($name, $value)); ?>"
           <?php echo e($required ? 'required' : ''); ?>>
    
    <!-- Error Display -->
    <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('quillEditor', (editorId, hiddenInputId, placeholder) => ({
        quill: null,
        
        init() {
            this.$nextTick(() => {
                this.initializeQuill(editorId, hiddenInputId, placeholder);
            });
        },
        
        initializeQuill(editorId, hiddenInputId, placeholder) {
            const editorElement = document.getElementById(editorId);
            const hiddenInput = document.getElementById(hiddenInputId);
            
            if (!editorElement || !hiddenInput) return;
            
            // Initialize Quill
            this.quill = new Quill(`#${editorId}`, {
                theme: 'snow',
                placeholder: placeholder,
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'font': [] }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'align': [] }],
                        ['blockquote', 'code-block'],
                        ['link', 'image', 'video'],
                        ['clean']
                    ]
                }
            });
            
            // Set initial content
            if (hiddenInput.value) {
                this.quill.root.innerHTML = hiddenInput.value;
            }
            
            // Update hidden input on content change
            this.quill.on('text-change', () => {
                hiddenInput.value = this.quill.root.innerHTML;
            });
            
            // Handle form submission
            const form = editorElement.closest('form');
            if (form) {
                form.addEventListener('submit', () => {
                    hiddenInput.value = this.quill.root.innerHTML;
                });
            }
        }
    }));
});
</script>
<?php /**PATH C:\laragon\www\qualifyrs\resources\views/components/quill-editor.blade.php ENDPATH**/ ?>