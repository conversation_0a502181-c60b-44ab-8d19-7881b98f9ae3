@props([
    'name' => 'content',
    'value' => '',
    'placeholder' => 'Start writing...',
    'height' => '300px',
    'required' => false,
    'id' => null
])

@php
    $editorId = $id ?? 'quill-editor-' . Str::random(8);
    $hiddenInputId = $name . '-hidden';
@endphp

<div class="quill-editor-wrapper" x-data="quillEditor('{{ $editorId }}', '{{ $hiddenInputId }}', '{{ $placeholder }}')">
    <!-- Quill Editor Container -->
    <div id="{{ $editorId }}" style="min-height: {{ $height }}"></div>
    
    <!-- Hidden Input for Form Submission -->
    <input type="hidden" 
           name="{{ $name }}" 
           id="{{ $hiddenInputId }}"
           value="{{ old($name, $value) }}"
           {{ $required ? 'required' : '' }}>
    
    <!-- Error Display -->
    @error($name)
        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
    @enderror
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('quillEditor', (editorId, hiddenInputId, placeholder) => ({
        quill: null,
        
        init() {
            this.$nextTick(() => {
                this.initializeQuill(editorId, hiddenInputId, placeholder);
            });
        },
        
        initializeQuill(editorId, hiddenInputId, placeholder) {
            const editorElement = document.getElementById(editorId);
            const hiddenInput = document.getElementById(hiddenInputId);
            
            if (!editorElement || !hiddenInput) return;
            
            // Initialize Quill
            this.quill = new Quill(`#${editorId}`, {
                theme: 'snow',
                placeholder: placeholder,
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'font': [] }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'align': [] }],
                        ['blockquote', 'code-block'],
                        ['link', 'image', 'video'],
                        ['clean']
                    ]
                }
            });
            
            // Set initial content
            if (hiddenInput.value) {
                this.quill.root.innerHTML = hiddenInput.value;
            }
            
            // Update hidden input on content change
            this.quill.on('text-change', () => {
                hiddenInput.value = this.quill.root.innerHTML;
            });
            
            // Handle form submission
            const form = editorElement.closest('form');
            if (form) {
                form.addEventListener('submit', () => {
                    hiddenInput.value = this.quill.root.innerHTML;
                });
            }
        }
    }));
});
</script>
