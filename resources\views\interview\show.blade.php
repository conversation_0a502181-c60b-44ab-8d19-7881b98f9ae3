@extends('layouts.app')

@section('title', $question->title . ' - Interview Questions')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('interview.index') }}" class="text-gray-700 hover:text-blue-600">
                        Interview Questions
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{{ route('interview.category', $question->category->slug) }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                            {{ $question->category->category }}
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">{{ Str::limit($question->title, 50) }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Question Card -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <!-- Question Header -->
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $question->title }}</h1>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ $question->category->category }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                        @else bg-red-100 text-red-800 @endif">
                        {{ ucfirst($question->difficulty) }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ ucfirst(str_replace('_', ' ', $question->type)) }}
                    </span>
                    <span>{{ $question->views }} views</span>
                    <span>{{ $question->created_at->diffForHumans() }}</span>
                </div>
            </div>

            <!-- Question Content -->
            <div class="prose max-w-none mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Question:</h2>
                <div class="text-gray-700 text-lg leading-relaxed mb-6">
                    {!! nl2br(e($question->question)) !!}
                </div>

                @if($question->type === 'multiple_choice' && $question->options->count() > 0)
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Options:</h3>
                    <div class="space-y-2 mb-6">
                        @foreach($question->options as $option)
                            <div class="flex items-start space-x-3 p-3 rounded-lg {{ $option->is_correct ? 'bg-green-50 border border-green-200' : 'bg-gray-50' }}">
                                <span class="flex-shrink-0 w-6 h-6 rounded-full {{ $option->is_correct ? 'bg-green-500' : 'bg-gray-300' }} text-white text-sm flex items-center justify-center font-medium">
                                    {{ chr(65 + $loop->index) }}
                                </span>
                                <span class="text-gray-700">{{ $option->option_text }}</span>
                                @if($option->is_correct)
                                    <span class="ml-auto text-green-600 text-sm font-medium">✓ Correct</span>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif

                @if($question->answer)
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Answer:</h3>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="text-gray-700">
                            {!! nl2br(e($question->answer)) !!}
                        </div>
                    </div>
                @endif

                @if($question->explanation)
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Explanation:</h3>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="text-gray-700">
                            {!! nl2br(e($question->explanation)) !!}
                        </div>
                    </div>
                @endif
            </div>

            <!-- Author Info -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-medium">{{ substr($question->author->name, 0, 1) }}</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">{{ $question->author->name }}</p>
                        <p class="text-sm text-gray-500">Published {{ $question->created_at->format('M j, Y') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Questions -->
        @if($relatedQuestions->count() > 0)
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Questions</h2>
                <div class="space-y-4">
                    @foreach($relatedQuestions as $related)
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <h3 class="font-semibold text-gray-900 mb-2">
                                <a href="{{ route('interview.show', $related->id) }}" class="hover:text-blue-600">
                                    {{ $related->title }}
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-2">{{ Str::limit($related->question, 150) }}</p>
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium 
                                    @if($related->difficulty === 'beginner') bg-green-100 text-green-800
                                    @elseif($related->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                    @elseif($related->difficulty === 'advanced') bg-orange-100 text-orange-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ ucfirst($related->difficulty) }}
                                </span>
                                <span>{{ $related->views }} views</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
