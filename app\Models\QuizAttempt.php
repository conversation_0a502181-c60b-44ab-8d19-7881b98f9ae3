<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QuizAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'quiz_id',
        'user_id',
        'score',
        'max_score',
        'percentage',
        'passed',
        'started_at',
        'completed_at',
        'time_taken',
        'answers',
        'results'
    ];

    protected $casts = [
        'passed' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'answers' => 'array',
        'results' => 'array',
    ];

    // Relationships
    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopePassed($query)
    {
        return $query->where('passed', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('passed', false);
    }

    // Helper methods
    public function isCompleted()
    {
        return !is_null($this->completed_at);
    }

    public function getTimeTakenFormatted()
    {
        if (!$this->time_taken) {
            return 'N/A';
        }

        $minutes = floor($this->time_taken / 60);
        $seconds = $this->time_taken % 60;

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    public function calculateResults()
    {
        if (!$this->answers || !$this->quiz) {
            return;
        }

        $totalQuestions = $this->quiz->getTotalQuestions();
        $correctAnswers = 0;
        $totalPoints = 0;
        $maxPoints = $this->quiz->getMaxScore();

        foreach ($this->answers as $questionId => $userAnswer) {
            $question = Question::find($questionId);
            if (!$question) continue;

            $pivotData = $this->quiz->questions()->where('question_id', $questionId)->first();
            $points = $pivotData ? $pivotData->pivot->points : 1;

            if ($this->isAnswerCorrect($question, $userAnswer)) {
                $correctAnswers++;
                $totalPoints += $points;
            }
        }

        $this->score = $totalPoints;
        $this->max_score = $maxPoints;
        $this->percentage = $maxPoints > 0 ? ($totalPoints / $maxPoints) * 100 : 0;
        $this->passed = $this->percentage >= $this->quiz->passing_score;
    }

    private function isAnswerCorrect($question, $userAnswer)
    {
        switch ($question->type) {
            case 'multiple_choice':
                $correctOptions = $question->options()->where('is_correct', true)->pluck('id')->toArray();
                return is_array($userAnswer)
                    ? empty(array_diff($correctOptions, $userAnswer)) && empty(array_diff($userAnswer, $correctOptions))
                    : in_array($userAnswer, $correctOptions);

            case 'true_false':
                return strtolower($userAnswer) === strtolower($question->answer);

            case 'text':
                return strtolower(trim($userAnswer)) === strtolower(trim($question->answer));

            default:
                return false;
        }
    }
}
