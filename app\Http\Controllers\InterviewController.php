<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Question;
use App\Models\Category;
use App\Models\Tag;

class InterviewController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::active()
            ->whereNull('parent_category')
            ->with(['children' => function($query) {
                $query->active()->orderBy('sort_order');
            }])
            ->orderBy('sort_order')
            ->get();

        $questions = Question::active()
            ->with(['category', 'author'])
            ->when($request->category, function($query, $category) {
                $query->whereHas('category', function($q) use ($category) {
                    $q->where('slug', $category);
                });
            })
            ->when($request->difficulty, function($query, $difficulty) {
                $query->where('difficulty', $difficulty);
            })
            ->when($request->search, function($query, $search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('question', 'like', "%{$search}%");
                });
            })
            ->latest()
            ->paginate(12);

        $popularTags = Tag::active()
            ->popular(10)
            ->get();

        return view('interview.index', compact('categories', 'questions', 'popularTags'));
    }

    public function show(Question $question)
    {
        $question->load(['category', 'author', 'options']);
        $question->incrementViews();

        $relatedQuestions = Question::active()
            ->where('category_id', $question->category_id)
            ->where('id', '!=', $question->id)
            ->limit(5)
            ->get();

        return view('interview.show', compact('question', 'relatedQuestions'));
    }

    public function category(Category $category)
    {
        $category->load(['children', 'parent']);

        $questions = Question::active()
            ->where('category_id', $category->id)
            ->orWhereHas('category', function($query) use ($category) {
                $query->where('parent_category', $category->id);
            })
            ->with(['category', 'author'])
            ->latest()
            ->paginate(12);

        $subcategories = $category->children()->active()->get();

        return view('interview.category', compact('category', 'questions', 'subcategories'));
    }

    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('interview.index');
        }

        $questions = Question::active()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('question', 'like', "%{$query}%")
                  ->orWhere('answer', 'like', "%{$query}%");
            })
            ->with(['category', 'author'])
            ->latest()
            ->paginate(12);

        return view('interview.search', compact('questions', 'query'));
    }

    public function byDifficulty($difficulty)
    {
        $validDifficulties = ['beginner', 'intermediate', 'advanced', 'expert'];

        if (!in_array($difficulty, $validDifficulties)) {
            abort(404);
        }

        $questions = Question::active()
            ->where('difficulty', $difficulty)
            ->with(['category', 'author'])
            ->latest()
            ->paginate(12);

        return view('interview.difficulty', compact('questions', 'difficulty'));
    }

    public function byTag(Tag $tag)
    {
        $questions = Question::active()
            ->whereHas('tags', function($query) use ($tag) {
                $query->where('tag_id', $tag->id);
            })
            ->with(['category', 'author'])
            ->latest()
            ->paginate(12);

        return view('interview.tag', compact('questions', 'tag'));
    }
}
