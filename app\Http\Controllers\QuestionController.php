<?php

namespace App\Http\Controllers;

use App\Models\Question;
use App\Models\Category;
use App\Models\Tag;
use App\Models\QuestionOption;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class QuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $questions = Question::with(['category', 'author', 'tags'])
            ->when(request('search'), function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('question', 'like', "%{$search}%");
            })
            ->when(request('category'), function ($query, $category) {
                $query->whereHas('category', function ($q) use ($category) {
                    $q->where('slug', $category);
                });
            })
            ->when(request('difficulty'), function ($query, $difficulty) {
                $query->where('difficulty', $difficulty);
            })
            ->when(request('type'), function ($query, $type) {
                $query->where('type', $type);
            })
            ->latest()
            ->paginate(15);

        $categories = Category::all();
        $difficulties = ['beginner', 'intermediate', 'advanced', 'expert'];
        $types = ['multiple_choice', 'true_false', 'text', 'coding'];

        return view('backend.questions.index', compact('questions', 'categories', 'difficulties', 'types'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        $tags = Tag::all();
        $difficulties = ['beginner', 'intermediate', 'advanced', 'expert'];
        $types = ['multiple_choice', 'true_false', 'text', 'coding'];

        return view('backend.questions.create', compact('categories', 'tags', 'difficulties', 'types'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'question' => 'required|string',
            'answer' => 'nullable|string',
            'explanation' => 'nullable|string',
            'type' => 'required|in:multiple_choice,true_false,text,coding',
            'difficulty' => 'required|in:beginner,intermediate,advanced,expert',
            'category_id' => 'required|exists:categories,id',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id',
            'options' => 'array',
            'options.*.text' => 'required_if:type,multiple_choice|string',
            'options.*.is_correct' => 'boolean',
        ]);

        $question = Question::create([
            'title' => $request->title,
            'question' => $request->question,
            'answer' => $request->answer,
            'explanation' => $request->explanation,
            'type' => $request->type,
            'difficulty' => $request->difficulty,
            'category_id' => $request->category_id,
            'author_id' => auth()->id(),
            'status' => true,
        ]);

        // Attach tags
        if ($request->tags) {
            $question->tags()->attach($request->tags);
        }

        // Create options for multiple choice questions
        if ($request->type === 'multiple_choice' && $request->options) {
            foreach ($request->options as $index => $option) {
                if (!empty($option['text'])) {
                    QuestionOption::create([
                        'question_id' => $question->id,
                        'option_text' => $option['text'],
                        'is_correct' => isset($option['is_correct']) ? true : false,
                        'sort_order' => $index + 1,
                    ]);
                }
            }
        }

        return redirect()->route('questions.index')
            ->with('success', 'Question created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Question $question)
    {
        $question->load(['category', 'author', 'tags', 'options']);
        $question->increment('views');

        return view('backend.questions.show', compact('question'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Question $question)
    {
        $question->load(['tags', 'options']);
        $categories = Category::all();
        $tags = Tag::all();
        $difficulties = ['beginner', 'intermediate', 'advanced', 'expert'];
        $types = ['multiple_choice', 'true_false', 'text', 'coding'];

        return view('backend.questions.edit', compact('question', 'categories', 'tags', 'difficulties', 'types'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Question $question)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'question' => 'required|string',
            'answer' => 'nullable|string',
            'explanation' => 'nullable|string',
            'type' => 'required|in:multiple_choice,true_false,text,coding',
            'difficulty' => 'required|in:beginner,intermediate,advanced,expert',
            'category_id' => 'required|exists:categories,id',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id',
            'options' => 'array',
            'options.*.text' => 'required_if:type,multiple_choice|string',
            'options.*.is_correct' => 'boolean',
        ]);

        $question->update([
            'title' => $request->title,
            'question' => $request->question,
            'answer' => $request->answer,
            'explanation' => $request->explanation,
            'type' => $request->type,
            'difficulty' => $request->difficulty,
            'category_id' => $request->category_id,
        ]);

        // Sync tags
        $question->tags()->sync($request->tags ?? []);

        // Update options for multiple choice questions
        if ($request->type === 'multiple_choice') {
            // Delete existing options
            $question->options()->delete();

            // Create new options
            if ($request->options) {
                foreach ($request->options as $index => $option) {
                    if (!empty($option['text'])) {
                        QuestionOption::create([
                            'question_id' => $question->id,
                            'option_text' => $option['text'],
                            'is_correct' => isset($option['is_correct']) ? true : false,
                            'sort_order' => $index + 1,
                        ]);
                    }
                }
            }
        } else {
            // Delete options if question type is not multiple choice
            $question->options()->delete();
        }

        return redirect()->route('questions.index')
            ->with('success', 'Question updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Question $question)
    {
        $question->delete();

        return redirect()->route('questions.index')
            ->with('success', 'Question deleted successfully.');
    }
}
