<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <span class="inline-flex items-center">
                    <span class="w-4 h-4 rounded-full mr-2" style="background-color: {{ $tag->color }};"></span>
                    {{ __('Edit Tag: ') . $tag->name }}
                </span>
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('tags.show', $tag) }}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    View Tag
                </a>
                <a href="{{ route('tags.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Tags
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('tags.update', $tag) }}" method="POST" x-data="tagForm()">
                        @csrf
                        @method('PUT')

                        <div class="space-y-6">
                            <!-- Tag Information -->
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Tag Information</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div><strong>Created:</strong> {{ $tag->created_at->format('M j, Y g:i A') }}</div>
                                    <div><strong>Updated:</strong> {{ $tag->updated_at->format('M j, Y g:i A') }}</div>
                                    <div><strong>Usage Count:</strong> {{ $tag->usage_count }}</div>
                                    <div><strong>Pages:</strong> {{ $tag->pages->count() }}</div>
                                </div>
                            </div>

                            <!-- Tag Name -->
                            <div>
                                <x-input-label for="name" :value="__('Tag Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" 
                                              :value="old('name', $tag->name)" required autofocus 
                                              x-model="name" @input="generateSlug" />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <!-- Slug -->
                            <div>
                                <x-input-label for="slug" :value="__('Slug')" />
                                <x-text-input id="slug" name="slug" type="text" class="mt-1 block w-full" 
                                              :value="old('slug', $tag->slug)" x-model="slug" />
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the tag name.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('slug')" />
                            </div>

                            <!-- Color -->
                            <div>
                                <x-input-label for="color" :value="__('Color')" />
                                <div class="mt-1 flex items-center space-x-4">
                                    <input id="color" name="color" type="color" 
                                           class="h-10 w-20 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" 
                                           :value="old('color', $tag->color)" x-model="color" @input="updatePreview">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600">Preview:</span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                              :style="`background-color: ${color}20; color: ${color};`"
                                              x-text="name || 'Sample Tag'">
                                        </span>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Choose a color that represents this tag.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('color')" />
                            </div>

                            <!-- Predefined Colors -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Colors</label>
                                <div class="grid grid-cols-8 gap-2">
                                    <template x-for="presetColor in presetColors" :key="presetColor">
                                        <button type="button" 
                                                class="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                :style="`background-color: ${presetColor}`"
                                                @click="color = presetColor; updatePreview()"
                                                :class="{ 'ring-2 ring-indigo-500': color === presetColor }">
                                        </button>
                                    </template>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <x-input-label for="description" :value="__('Description')" />
                                <textarea id="description" name="description" rows="3" 
                                          class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                          placeholder="Brief description of this tag...">{{ old('description', $tag->description) }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                            </div>

                            <!-- Status -->
                            <div class="flex items-center">
                                <input id="status" name="status" type="checkbox" value="1" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                       {{ old('status', $tag->status) ? 'checked' : '' }}>
                                <label for="status" class="ml-2 block text-sm text-gray-900">
                                    Active (available for use)
                                </label>
                            </div>

                            <!-- Warning Messages -->
                            @if($tag->pages->count() > 0)
                                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-blue-800">
                                                This tag is used in {{ $tag->pages->count() }} pages
                                            </h3>
                                            <div class="mt-2 text-sm text-blue-700">
                                                <p>Changes to the tag name and color will be reflected across all pages using this tag.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Update Tag') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function tagForm() {
            return {
                name: '{{ old('name', $tag->name) }}',
                slug: '{{ old('slug', $tag->slug) }}',
                color: '{{ old('color', $tag->color) }}',
                presetColors: [
                    '#EF4444', '#F97316', '#F59E0B', '#EAB308',
                    '#84CC16', '#22C55E', '#10B981', '#14B8A6',
                    '#06B6D4', '#0EA5E9', '#3B82F6', '#6366F1',
                    '#8B5CF6', '#A855F7', '#D946EF', '#EC4899'
                ],
                
                generateSlug() {
                    // Only auto-generate if slug is empty
                    if (this.name && !this.slug) {
                        this.slug = this.name.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                },
                
                updatePreview() {
                    // This method is called when color changes to update the preview
                }
            }
        }
    </script>
</x-app-layout>
