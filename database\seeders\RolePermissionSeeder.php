<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard
            'view-dashboard',

            // User Management
            'manage-users',
            'manage-roles',
            'manage-permissions',

            // Page Management
            'manage-pages',
            'create-pages',
            'edit-pages',
            'delete-pages',
            'publish-pages',

            // Question Management
            'manage-questions',
            'create-questions',
            'edit-questions',
            'delete-questions',
            'publish-questions',

            // Quiz Management
            'manage-quizzes',
            'create-quizzes',
            'edit-quizzes',
            'delete-quizzes',
            'publish-quizzes',
            'view-quiz-results',

            // Category Management
            'manage-categories',
            'create-categories',
            'edit-categories',
            'delete-categories',

            // Tag Management
            'manage-tags',
            'create-tags',
            'edit-tags',
            'delete-tags',

            // Public Access
            'take-quizzes',
            'view-questions',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::all());

        $managerRole->givePermissionTo([
            'view-dashboard',
            'manage-pages',
            'create-pages',
            'edit-pages',
            'delete-pages',
            'publish-pages',
            'manage-questions',
            'create-questions',
            'edit-questions',
            'delete-questions',
            'publish-questions',
            'manage-quizzes',
            'create-quizzes',
            'edit-quizzes',
            'delete-quizzes',
            'publish-quizzes',
            'view-quiz-results',
            'manage-categories',
            'create-categories',
            'edit-categories',
            'delete-categories',
            'manage-tags',
            'create-tags',
            'edit-tags',
            'delete-tags',
            'take-quizzes',
            'view-questions',
        ]);

        $userRole->givePermissionTo([
            'view-dashboard',
            'take-quizzes',
            'view-questions',
        ]);

        // Create admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        $adminUser->assignRole('admin');

        // Create manager user
        $managerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        $managerUser->assignRole('manager');

        // Create regular user
        $regularUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        $regularUser->assignRole('user');
    }
}
