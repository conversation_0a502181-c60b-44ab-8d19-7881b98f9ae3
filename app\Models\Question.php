<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Question extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'question',
        'answer',
        'explanation',
        'type',
        'difficulty',
        'category_id',
        'author_id',
        'status',
        'views',
        'usage_count',
        'metadata'
    ];

    protected $casts = [
        'status' => 'boolean',
        'metadata' => 'array',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function options()
    {
        return $this->hasMany(QuestionOption::class)->orderBy('sort_order');
    }

    public function quizzes()
    {
        return $this->belongsToMany(Quiz::class)->withPivot('sort_order', 'points')->withTimestamps();
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeByDifficulty($query, $difficulty)
    {
        return $query->where('difficulty', $difficulty);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    // Helper methods
    public function getCorrectAnswer()
    {
        if ($this->type === 'multiple_choice') {
            return $this->options()->where('is_correct', true)->first();
        }
        return $this->answer;
    }

    public function getCorrectAnswers()
    {
        if ($this->type === 'multiple_choice') {
            return $this->options()->where('is_correct', true)->get();
        }
        return collect([$this->answer]);
    }

    public function incrementViews()
    {
        $this->increment('views');
    }

    public function incrementUsage()
    {
        $this->increment('usage_count');
    }
}
