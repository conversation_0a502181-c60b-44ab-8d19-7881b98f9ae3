<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit User: ') . $user->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('users.show', $user) }}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    View User
                </a>
                <a href="{{ route('users.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Users
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('users.update', $user) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="space-y-6">
                            <!-- User Information -->
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div><strong>User ID:</strong> {{ $user->id }}</div>
                                    <div><strong>Created:</strong> {{ $user->created_at->format('M j, Y') }}</div>
                                    <div><strong>Last Updated:</strong> {{ $user->updated_at->format('M j, Y') }}</div>
                                    <div><strong>Email Verified:</strong> {{ $user->email_verified_at ? 'Yes' : 'No' }}</div>
                                </div>
                            </div>

                            <!-- Name -->
                            <div>
                                <x-input-label for="name" :value="__('Full Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" 
                                              :value="old('name', $user->name)" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <!-- Email -->
                            <div>
                                <x-input-label for="email" :value="__('Email Address')" />
                                <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" 
                                              :value="old('email', $user->email)" required />
                                <x-input-error class="mt-2" :messages="$errors->get('email')" />
                            </div>

                            <!-- Password -->
                            <div>
                                <x-input-label for="password" :value="__('New Password')" />
                                <x-text-input id="password" name="password" type="password" class="mt-1 block w-full" />
                                <p class="mt-1 text-sm text-gray-500">Leave blank to keep current password. Must be at least 8 characters if changing.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('password')" />
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <x-input-label for="password_confirmation" :value="__('Confirm New Password')" />
                                <x-text-input id="password_confirmation" name="password_confirmation" type="password" class="mt-1 block w-full" />
                                <x-input-error class="mt-2" :messages="$errors->get('password_confirmation')" />
                            </div>

                            <!-- Roles -->
                            <div>
                                <x-input-label for="roles" :value="__('User Roles')" />
                                <div class="mt-2 space-y-2">
                                    @foreach($roles as $role)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="roles[]" value="{{ $role->name }}" 
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                   {{ in_array($role->name, old('roles', $user->roles->pluck('name')->toArray())) ? 'checked' : '' }}>
                                            <span class="ml-2 text-sm text-gray-700">
                                                <span class="font-medium">{{ ucfirst($role->name) }}</span>
                                                @if($role->name === 'admin')
                                                    <span class="text-gray-500">- Full system access</span>
                                                @elseif($role->name === 'editor')
                                                    <span class="text-gray-500">- Can create and edit content</span>
                                                @else
                                                    <span class="text-gray-500">- Basic user access</span>
                                                @endif
                                            </span>
                                        </label>
                                    @endforeach
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Select one or more roles for this user.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('roles')" />
                            </div>

                            <!-- Email Verification -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Email Verification</h3>
                                <div class="flex items-center">
                                    <input id="email_verified" name="email_verified" type="checkbox" value="1" 
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                           {{ old('email_verified', $user->email_verified_at ? true : false) ? 'checked' : '' }}>
                                    <label for="email_verified" class="ml-2 block text-sm text-gray-700">
                                        Mark email as verified
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">
                                    @if($user->email_verified_at)
                                        Currently verified on {{ $user->email_verified_at->format('M j, Y g:i A') }}
                                    @else
                                        Email is currently not verified
                                    @endif
                                </p>
                            </div>

                            <!-- Account Settings -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Account Settings</h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input id="send_notification_email" name="send_notification_email" type="checkbox" value="1" 
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                               {{ old('send_notification_email') ? 'checked' : '' }}>
                                        <label for="send_notification_email" class="ml-2 block text-sm text-gray-700">
                                            Send notification email about changes
                                        </label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="force_password_change" name="force_password_change" type="checkbox" value="1" 
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                               {{ old('force_password_change') ? 'checked' : '' }}>
                                        <label for="force_password_change" class="ml-2 block text-sm text-gray-700">
                                            Require password change on next login
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Warning Messages -->
                            @if($user->id === auth()->id())
                                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">
                                                You are editing your own account
                                            </h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p>Be careful when changing your own roles or email address. You may lose access to certain features.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($user->hasRole('admin') && \App\Models\User::role('admin')->count() === 1)
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">
                                                This is the only admin user
                                            </h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>Removing admin role from this user will leave the system without any administrators.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Update User') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
