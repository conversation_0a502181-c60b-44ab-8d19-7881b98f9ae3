<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name', 'Laravel') }} - All Pages</title>
    <meta name="description" content="Browse all published pages and articles">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900">{{ config('app.name', 'Laravel') }}</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('pages.index') }}" class="text-gray-700 hover:text-gray-900">All Pages</a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-gray-900">Dashboard</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-gray-900">Logout</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-gray-900">Login</a>
                        <a href="{{ route('register') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Register</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900">All Pages</h1>
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="relative" x-data="{ open: false }">
                        <input type="text" placeholder="Search pages..." 
                               class="w-64 px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                               @focus="open = true" @blur="setTimeout(() => open = false, 200)">
                    </div>
                    <!-- Filter -->
                    <select class="px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">All Categories</option>
                        @foreach(\App\Models\Category::active()->orderBy('category')->get() as $category)
                            <option value="{{ $category->slug }}">{{ $category->category }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        @if($pages->count() > 0)
            <!-- Featured Pages -->
            @php
                $featuredPages = $pages->where('featured', true)->take(3);
            @endphp
            
            @if($featuredPages->count() > 0)
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Featured Pages</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        @foreach($featuredPages as $page)
                            <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        @if($page->category)
                                            <a href="{{ route('pages.category', $page->category->slug) }}" 
                                               class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
                                                {{ $page->category->category }}
                                            </a>
                                        @endif
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Featured
                                        </span>
                                    </div>
                                    
                                    <h3 class="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                                        <a href="{{ $page->category ? route('pages.show', [$page->category->slug, $page->slug]) : route('pages.show.simple', $page->slug) }}" 
                                           class="hover:text-indigo-600">
                                            {{ $page->title }}
                                        </a>
                                    </h3>
                                    
                                    <p class="text-gray-600 mb-4 line-clamp-3">
                                        {{ Str::limit(strip_tags($page->content), 150) }}
                                    </p>
                                    
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        <span>By {{ $page->author->name }}</span>
                                        <span>{{ $page->created_at->format('M j, Y') }}</span>
                                    </div>
                                    
                                    @if($page->tags->count() > 0)
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            @foreach($page->tags->take(3) as $tag)
                                                <a href="{{ route('pages.tag', $tag->slug) }}" 
                                                   class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium hover:opacity-80"
                                                   style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                                    {{ $tag->name }}
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            {{ $page->views }} views
                                        </div>
                                        <a href="{{ $page->category ? route('pages.show', [$page->category->slug, $page->slug]) : route('pages.show.simple', $page->slug) }}" 
                                           class="inline-flex items-center text-indigo-600 hover:text-indigo-800 font-medium">
                                            Read More
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>
                </section>
            @endif

            <!-- All Pages -->
            <section>
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">All Pages</h2>
                    <div class="text-sm text-gray-500">
                        Showing {{ $pages->firstItem() }}-{{ $pages->lastItem() }} of {{ $pages->total() }} pages
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($pages as $page)
                        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    @if($page->category)
                                        <a href="{{ route('pages.category', $page->category->slug) }}" 
                                           class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200">
                                            {{ $page->category->category }}
                                        </a>
                                    @endif
                                    @if($page->featured)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Featured
                                        </span>
                                    @endif
                                </div>
                                
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="{{ $page->category ? route('pages.show', [$page->category->slug, $page->slug]) : route('pages.show.simple', $page->slug) }}" 
                                       class="hover:text-indigo-600">
                                        {{ $page->title }}
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                    {{ Str::limit(strip_tags($page->content), 100) }}
                                </p>
                                
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                    <span>{{ $page->author->name }}</span>
                                    <span>{{ $page->created_at->format('M j, Y') }}</span>
                                </div>
                                
                                @if($page->tags->count() > 0)
                                    <div class="flex flex-wrap gap-1 mb-3">
                                        @foreach($page->tags->take(2) as $tag)
                                            <a href="{{ route('pages.tag', $tag->slug) }}" 
                                               class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium hover:opacity-80"
                                               style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                                {{ $tag->name }}
                                            </a>
                                        @endforeach
                                        @if($page->tags->count() > 2)
                                            <span class="text-xs text-gray-400">+{{ $page->tags->count() - 2 }}</span>
                                        @endif
                                    </div>
                                @endif
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-xs text-gray-500">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        {{ $page->views }}
                                    </div>
                                    <a href="{{ $page->category ? route('pages.show', [$page->category->slug, $page->slug]) : route('pages.show.simple', $page->slug) }}" 
                                       class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                        Read →
                                    </a>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="mt-8">
                    {{ $pages->links() }}
                </div>
            </section>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No pages found</h3>
                <p class="mt-1 text-sm text-gray-500">There are no published pages available at the moment.</p>
            </div>
        @endif
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500 text-sm">
                © {{ date('Y') }} {{ config('app.name', 'Laravel') }}. All rights reserved.
            </div>
        </div>
    </footer>
</body>
</html>
