<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Category') }}
            </h2>
            <a href="{{ route('categories.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Back to Categories
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('categories.store') }}" method="POST" x-data="categoryForm()">
                        @csrf

                        <div class="space-y-6">
                            <!-- Category Name -->
                            <div>
                                <x-input-label for="category" :value="__('Category Name')" />
                                <x-text-input id="category" name="category" type="text" class="mt-1 block w-full" 
                                              :value="old('category')" required autofocus 
                                              x-model="category" @input="generateSlug" />
                                <x-input-error class="mt-2" :messages="$errors->get('category')" />
                            </div>

                            <!-- Slug -->
                            <div>
                                <x-input-label for="slug" :value="__('Slug')" />
                                <x-text-input id="slug" name="slug" type="text" class="mt-1 block w-full" 
                                              :value="old('slug')" x-model="slug" />
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the category name. Leave blank to auto-generate.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('slug')" />
                            </div>

                            <!-- Parent Category -->
                            <div>
                                <x-input-label for="parent_category" :value="__('Parent Category')" />
                                <select id="parent_category" name="parent_category" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                    <option value="">None (Root Category)</option>
                                    @foreach($parentCategories as $parentCategory)
                                        <option value="{{ $parentCategory->id }}" {{ old('parent_category') == $parentCategory->id ? 'selected' : '' }}>
                                            {{ $parentCategory->category }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-sm text-gray-500">Select a parent category to create a subcategory.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('parent_category')" />
                            </div>

                            <!-- Description -->
                            <div>
                                <x-input-label for="description" :value="__('Description')" />
                                <textarea id="description" name="description" rows="4" 
                                          class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                          placeholder="Brief description of this category...">{{ old('description') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <x-input-label for="sort_order" :value="__('Sort Order')" />
                                <x-text-input id="sort_order" name="sort_order" type="number" class="mt-1 block w-full" 
                                              :value="old('sort_order', 0)" min="0" />
                                <p class="mt-1 text-sm text-gray-500">Lower numbers appear first. Default is 0.</p>
                                <x-input-error class="mt-2" :messages="$errors->get('sort_order')" />
                            </div>

                            <!-- Status -->
                            <div class="flex items-center">
                                <input id="status" name="status" type="checkbox" value="1" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                       {{ old('status', true) ? 'checked' : '' }}>
                                <label for="status" class="ml-2 block text-sm text-gray-900">
                                    Active (visible to users)
                                </label>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <x-secondary-button type="button" onclick="window.history.back()">
                                    {{ __('Cancel') }}
                                </x-secondary-button>
                                <x-primary-button>
                                    {{ __('Create Category') }}
                                </x-primary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function categoryForm() {
            return {
                category: '',
                slug: '',
                generateSlug() {
                    if (this.category && !this.slug) {
                        this.slug = this.category.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                }
            }
        }
    </script>
</x-app-layout>
