@extends('layouts.app')

@section('title', 'Interview Questions - Qualifyrs')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Interview Questions</h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Prepare for your next job interview with our comprehensive collection of interview questions across various technologies and roles.
                </p>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <form action="{{ route('interview.search') }}" method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="q" value="{{ request('search') }}" 
                           placeholder="Search interview questions..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Search
                </button>
            </form>
        </div>

        <!-- Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            @foreach($categories as $category)
                <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="{{ route('interview.category', $category->slug) }}" class="hover:text-blue-600">
                                {{ $category->category }}
                            </a>
                        </h3>
                        <p class="text-gray-600 mb-4">{{ $category->description }}</p>
                        
                        @if($category->children->count() > 0)
                            <div class="space-y-2">
                                @foreach($category->children->take(3) as $child)
                                    <a href="{{ route('interview.category', $child->slug) }}" 
                                       class="block text-sm text-blue-600 hover:text-blue-800">
                                        {{ $child->category }}
                                    </a>
                                @endforeach
                                @if($category->children->count() > 3)
                                    <p class="text-sm text-gray-500">+{{ $category->children->count() - 3 }} more</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Difficulty Levels -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Browse by Difficulty</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="{{ route('interview.difficulty', 'beginner') }}" 
                   class="p-4 text-center bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <div class="text-green-600 font-semibold">Beginner</div>
                    <div class="text-sm text-green-500">Entry Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'intermediate') }}" 
                   class="p-4 text-center bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                    <div class="text-yellow-600 font-semibold">Intermediate</div>
                    <div class="text-sm text-yellow-500">Mid Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'advanced') }}" 
                   class="p-4 text-center bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <div class="text-orange-600 font-semibold">Advanced</div>
                    <div class="text-sm text-orange-500">Senior Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'expert') }}" 
                   class="p-4 text-center bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <div class="text-red-600 font-semibold">Expert</div>
                    <div class="text-sm text-red-500">Lead Level</div>
                </a>
            </div>
        </div>

        <!-- Recent Questions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Questions</h2>
            <div class="space-y-6">
                @forelse($questions as $question)
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('interview.show', $question->id) }}" class="hover:text-blue-600">
                                        {{ $question->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3">{{ Str::limit($question->question, 200) }}</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $question->category->category }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($question->difficulty) }}
                                    </span>
                                    <span>{{ $question->views }} views</span>
                                    <span>{{ $question->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg">No questions found.</div>
                        <p class="text-gray-400 mt-2">Check back later for new interview questions.</p>
                    </div>
                @endforelse
            </div>

            @if($questions->hasPages())
                <div class="mt-8">
                    {{ $questions->links() }}
                </div>
            @endif
        </div>

        <!-- Popular Tags -->
        @if($popularTags->count() > 0)
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Popular Tags</h2>
                <div class="flex flex-wrap gap-2">
                    @foreach($popularTags as $tag)
                        <a href="{{ route('interview.tag', $tag->slug) }}" 
                           class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white hover:opacity-80 transition-opacity"
                           style="background-color: {{ $tag->color }}">
                            {{ $tag->name }}
                            <span class="ml-1 text-xs opacity-75">({{ $tag->usage_count }})</span>
                        </a>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
